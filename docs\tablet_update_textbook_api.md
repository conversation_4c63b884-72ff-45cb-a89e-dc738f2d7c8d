# 平板更新教材接口文档

## 概述

本文档描述了在平板更新模块中实现的教材相关接口，这些接口模仿了您提供的 GoFrame 代码结构，但使用了当前项目的 Gin 框架和架构模式。

## 实现的接口

### 1. 获取教材选项

**接口地址**: `GET /apps/v1/update-history/default/v1/textbook/resource-options`

**功能**: 获取教材类型选项列表

**响应示例**:
```json
{
  "ok": 1,
  "msg": "ok",
  "data": [
    {
      "name": "双师直播课",
      "value": "sszb_course"
    },
    {
      "name": "名师辅导班", 
      "value": "msfd_course"
    },
    {
      "name": "真题试卷",
      "value": "paper"
    },
    {
      "name": "作业本教辅",
      "value": "homework"
    }
  ]
}
```

### 2. 获取教材历史记录

**接口地址**: `GET /apps/v1/update-history/default/v1/textbook/histories`

**功能**: 根据类型获取教材历史记录，支持分页

**请求参数**:
- `type` (string, required): 类型，可选值：sszb_course, msfd_course, paper, homework
- `page` (int, optional): 页码，默认1
- `page_size` (int, optional): 每页大小，默认20

**响应示例**:
```json
{
  "ok": 1,
  "msg": "ok", 
  "data": [
    {
      "updated_at": "2024-01-15",
      "items": [
        {
          "subject": "数学",
          "grades": ["三年级", "四年级"],
          "name": "小学数学同步课程"
        }
      ]
    }
  ]
}
```

## 实现细节

### 架构层次

1. **DTO层** (`internal/handler/dto/tablet_update.go`)
   - `TextBookHistoriesRequest`: 请求参数结构
   - `TextBookHistoryItem`: 历史记录项结构
   - `TextBookHistoriesGroupItem`: 分组历史记录结构
   - `TextBookOptionsResponse`: 选项响应结构

2. **Handler层** (`internal/handler/tablet_update.go`)
   - `GetTextBookOptions`: 处理获取教材选项请求
   - `GetTextBookHistories`: 处理获取教材历史记录请求

3. **Service层** (`internal/service/tablet_update.go`)
   - `GetTextBookOptions`: 业务逻辑处理
   - `GetTextBookHistories`: 历史记录查询和数据处理逻辑

4. **Repository层** (`internal/repository/tablet_update.go`)
   - `GetDistinctUpdateDates`: 获取不同的更新日期
   - `GetUpdatesByTypeAndDateRange`: 根据类型和日期范围获取记录

5. **路由层** (`internal/router/router.go`)
   - 注册了 `/update-history/default/v1/textbook` 路由组
   - 包含认证中间件和权限检查

### 数据处理逻辑

1. **日期分组**: 按更新日期对记录进行分组
2. **JSON解析**: 解析存储在数据库中的JSON数据
3. **类型适配**: 根据不同的教材类型返回相应的字段结构
4. **分页支持**: 支持分页查询，默认每页20条记录

### 权限控制

- 使用 `middleware.CheckAppPermission("app-update-history", ...)` 进行权限验证
- 需要有效的JWT token和相应的系统权限

## 与原代码的对应关系

| 原代码 (GoFrame) | 新实现 (Gin) |
|-----------------|-------------|
| `v1.TextBook.Options` | `tabletUpdateController.GetTextBookOptions` |
| `v1.TextBook.Index` | `tabletUpdateController.GetTextBookHistories` |
| `dao.AppNotificationTabletUpdate` | `tabletUpdateRepository` |
| `shared.Util.SuccessResponseList` | `handler.ResponseSuccess` |
| `shared.Util.ThrowError` | `handler.ResponseError` |

## 注意事项

1. 确保数据库中存在 `app_notification_tablet_update` 表
2. 需要配置相应的系统权限 `app-update-history`
3. JSON数据格式需要与原系统保持一致
4. 时间格式统一使用 `YYYY-MM-DD` 格式
