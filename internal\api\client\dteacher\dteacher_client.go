package dteacher

import (
	"encoding/json"
	"fmt"
	"io"
	"marketing-app/internal/api/client/dteacher/config"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
)

type DTeacherClient struct {
	httpClient *http.Client
	cfg        *config.AppConfig
}

func NewDTeacherClient(cfg *config.AppConfig) DTeacherClient {
	return DTeacherClient{
		httpClient: &http.Client{
			Timeout: cfg.HTTPTimeout,
		},
		cfg: cfg,
	}
}

// GetDTeacherData 获取双师数据
func (d *DTeacherClient) GetDTeacherData(c *gin.Context, userID string) (map[string]interface{}, error) {
	url := fmt.Sprintf("%s/report/v1/extra/data_2?userid=%s", d.cfg.Host, userID)

	// 创建请求
	req, err := http.NewRequestWithContext(c, "GET", url, nil)
	if err != nil {
		return nil, errors.Wrap(err, "创建请求失败")
	}

	// 发送请求
	resp, err := d.httpClient.Do(req)
	if err != nil {
		return nil, errors.Wrap(err, "调用DTeacher API失败")
	}
	defer func(Body io.ReadCloser) {
		_ = Body.Close()
	}(resp.Body)

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return nil, errors.New(fmt.Sprintf("DTeacher API响应错误，状态码: %d", resp.StatusCode))
	}

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, errors.Wrap(err, "读取响应失败")
	}

	// 解析JSON响应
	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, errors.Wrap(err, "解析JSON失败")
	}

	// 检查响应状态
	responseNo, ok := result["F_responseNo"].(float64)
	if !ok || responseNo != 10000 {
		errorMsg := "系统出错"
		if msg, exists := result["F_responseMsg"].(string); exists {
			errorMsg = msg
		}
		return nil, errors.New(fmt.Sprintf("API错误: %s", errorMsg))
	}

	// 移除系统字段
	delete(result, "F_responseNo")
	delete(result, "F_responseMsg")

	return result, nil
}

// GetDTeacherReportWeekly 获取双师周报数据
func (d *DTeacherClient) GetDTeacherReportWeekly(c *gin.Context, userID int, endDay int64) (map[string]interface{}, error) {
	url := fmt.Sprintf("%s/report/v1/wechat/weekly?ucid=%d&endday=%d", d.cfg.Host, userID, endDay)

	// 创建请求
	req, err := http.NewRequestWithContext(c, "GET", url, nil)
	if err != nil {
		return nil, errors.Wrap(err, "创建请求失败")
	}

	// 发送请求
	resp, err := d.httpClient.Do(req)
	if err != nil {
		return nil, errors.Wrap(err, "调用DTeacher API失败")
	}
	defer func(Body io.ReadCloser) {
		_ = Body.Close()
	}(resp.Body)

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return nil, errors.New(fmt.Sprintf("DTeacher API响应错误，状态码: %d", resp.StatusCode))
	}

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, errors.Wrap(err, "读取响应失败")
	}

	// 解析JSON响应
	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, errors.Wrap(err, "解析JSON失败")
	}

	// 检查响应状态
	responseNo, ok := result["F_responseNo"].(float64)
	if !ok || responseNo != 10000 {
		errorMsg := "系统出错"
		if msg, exists := result["F_responseMsg"].(string); exists {
			errorMsg = msg
		}
		return nil, errors.New(fmt.Sprintf("API错误: %s", errorMsg))
	}

	// 移除系统字段
	delete(result, "F_responseNo")
	delete(result, "F_responseMsg")

	return result, nil
}

// GetDTeacherReportWeeks 获取双师周数据列
// GetDTeacherReportWeeks 获取双师周数据列表
func (d *DTeacherClient) GetDTeacherReportWeeks(c *gin.Context, userID int) ([]interface{}, error) {
	url := fmt.Sprintf("%s/report/v1/wechat/weeks?ucid=%d", d.cfg.Host, userID)

	// 创建请求
	req, err := http.NewRequestWithContext(c, "GET", url, nil)
	if err != nil {
		return nil, errors.Wrap(err, "创建请求失败")
	}

	// 发送请求
	resp, err := d.httpClient.Do(req)
	if err != nil {
		return nil, errors.Wrap(err, "调用DTeacher API失败")
	}
	defer func(Body io.ReadCloser) {
		_ = Body.Close()
	}(resp.Body)

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return nil, errors.New(fmt.Sprintf("DTeacher API响应错误，状态码: %d", resp.StatusCode))
	}

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, errors.Wrap(err, "读取响应失败")
	}

	// 解析JSON响应
	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, errors.Wrap(err, "解析JSON失败")
	}

	// 检查响应状态
	responseNo, ok := result["F_responseNo"].(float64)
	if !ok || responseNo != 10000 {
		errorMsg := "系统出错"
		if msg, exists := result["F_responseMsg"].(string); exists {
			errorMsg = msg
		}
		return nil, errors.New(fmt.Sprintf("API错误: %s", errorMsg))
	}

	// 获取weeks数组
	weeks, ok := result["weeks"].([]interface{})
	if !ok {
		return nil, errors.New("响应中缺少weeks字段或格式不正确")
	}

	return weeks, nil
}
