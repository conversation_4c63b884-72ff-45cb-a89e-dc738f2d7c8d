package cache

import (
	"bytes"
	"compress/zlib"
	"encoding/json"
	"fmt"
	"io"
	"marketing-app/internal/pkg/redis"
	"time"

	"github.com/gin-gonic/gin"
)

const (
	// DTeacher cache key patterns
	DTEACHER_DATA          = "d_teacher:data:%s"
	DTEACHER_REPORT_WEEKLY = "d_teacher:report_weekly:%s:%s"
	DTEACHER_REPORT_WEEKS  = "d_teacher:report_weeks:%s"
)

type CustomerCache interface {
	// DTeacher data caching
	SetDTeacherData(c *gin.Context, data interface{}, uid string) error
	GetDTeacherData(c *gin.Context, uid string) (interface{}, error)

	// DTeacher weekly report caching
	SetDTeacherReportWeekly(c *gin.Context, data interface{}, uid string, date string) error
	GetDTeacherReportWeekly(c *gin.Context, uid string, date string) (interface{}, error)

	// DTeacher weeks report caching
	SetDTeacherReportWeeks(c *gin.Context, data interface{}, uid string) error
	GetDTeacherReportWeeks(c *gin.Context, uid string) (interface{}, error)
}

type customerCache struct {
}

func NewCustomerCache() CustomerCache {
	// Use the existing Redis package which handles configuration
	// No need to create our own client
	return &customerCache{}
}

func (cc *customerCache) todayRemainSeconds() int {
	now := time.Now()
	tomorrow := now.AddDate(0, 0, 1)
	endOfDay := time.Date(tomorrow.Year(), tomorrow.Month(), tomorrow.Day(), 0, 0, 0, 0, tomorrow.Location())
	return int(endOfDay.Sub(now).Seconds()) + 10
}

// compressData 压缩缓存数据
func (cc *customerCache) compressData(data interface{}) (string, error) {
	jsonBytes, err := json.Marshal(data)
	if err != nil {
		return "", err
	}

	var buf bytes.Buffer
	w := zlib.NewWriter(&buf)
	if _, err := w.Write(jsonBytes); err != nil {
		return "", err
	}
	if err := w.Close(); err != nil {
		return "", err
	}

	return buf.String(), nil
}

// decompressData 解压缓存数据
func (cc *customerCache) decompressData(compressedData string) (interface{}, error) {
	if compressedData == "" {
		return nil, nil
	}

	r, err := zlib.NewReader(bytes.NewReader([]byte(compressedData)))
	if err != nil {
		return nil, err
	}
	defer r.Close()

	decompressed, err := io.ReadAll(r)
	if err != nil {
		return nil, err
	}

	var data interface{}
	if err := json.Unmarshal(decompressed, &data); err != nil {
		return nil, err
	}

	return data, nil
}

// SetDTeacherData 设置压缩缓存数据
func (cc *customerCache) SetDTeacherData(c *gin.Context, data interface{}, uid string) error {
	key := fmt.Sprintf(DTEACHER_DATA, uid)
	expire := time.Duration(cc.todayRemainSeconds()) * time.Second

	compressedData, err := cc.compressData(data)
	if err != nil {
		return err
	}

	return redis.Set(c, key, compressedData, expire)
}

// GetDTeacherData 获取压缩缓存数据
func (cc *customerCache) GetDTeacherData(c *gin.Context, uid string) (interface{}, error) {
	key := fmt.Sprintf(DTEACHER_DATA, uid)

	result, err := redis.Get(c, key)
	if err != nil {
		return nil, err
	}

	if result == "" {
		return nil, nil // Cache miss
	}

	return cc.decompressData(result)
}

// SetDTeacherReportWeekly 设置压缩数据 DTeacher weekly report
func (cc *customerCache) SetDTeacherReportWeekly(c *gin.Context, data interface{}, uid string, date string) error {
	key := fmt.Sprintf(DTEACHER_REPORT_WEEKLY, uid, date)
	expire := time.Duration(cc.todayRemainSeconds()) * time.Second

	compressedData, err := cc.compressData(data)
	if err != nil {
		return err
	}

	return redis.Set(c, key, compressedData, expire)
}

// GetDTeacherReportWeekly 获取压缩数据 DTeacher weekly report
func (cc *customerCache) GetDTeacherReportWeekly(c *gin.Context, uid string, date string) (interface{}, error) {
	key := fmt.Sprintf(DTEACHER_REPORT_WEEKLY, uid, date)

	result, err := redis.Get(c, key)
	if err != nil {
		return nil, err
	}

	if result == "" {
		return nil, nil // Cache miss
	}

	return cc.decompressData(result)
}

// SetDTeacherReportWeeks 设置压缩数据 DTeacher weeks report
func (cc *customerCache) SetDTeacherReportWeeks(c *gin.Context, data interface{}, uid string) error {
	key := fmt.Sprintf(DTEACHER_REPORT_WEEKS, uid)
	expire := time.Duration(cc.todayRemainSeconds()) * time.Second

	compressedData, err := cc.compressData(data)
	if err != nil {
		return err
	}

	return redis.Set(c, key, compressedData, expire)
}

// GetDTeacherReportWeeks 获取压缩数据 DTeacher weeks report
func (cc *customerCache) GetDTeacherReportWeeks(c *gin.Context, uid string) (interface{}, error) {
	key := fmt.Sprintf(DTEACHER_REPORT_WEEKS, uid)

	result, err := redis.Get(c, key)
	if err != nil {
		return nil, err
	}

	if result == "" {
		return nil, nil // Cache miss
	}

	return cc.decompressData(result)
}
