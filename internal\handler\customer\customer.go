package customer

import (
	"encoding/json"
	"marketing-app/internal/handler"
	"marketing-app/internal/handler/customer/dto"
	"marketing-app/internal/service/customer"
	"marketing-app/internal/service/customer/entity"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
)

type Customer interface {
	GetDeviceUsedDetail(c *gin.Context)
	GetDeviceUsageSummary(c *gin.Context)
	GetDeviceUsageSummaryBatch(c *gin.Context)
	GetTutorUsageSummary(c *gin.Context)
	GetTutorUsageDetail(c *gin.Context)
	GetTutorsplanUsageSummary(c *gin.Context)
	GetTutorsplanUsageDetail(c *gin.Context)
	GetDTeacherData(c *gin.Context)
	GetDTeacherReportWeekly(c *gin.Context)
	GetDTeacherReportWeeks(c *gin.Context)
}

type customerHandler struct {
	customerSvc customer.CustomerSvc
}

func NewCustomerHandler(svc customer.CustomerSvc) Customer {
	return &customerHandler{customerSvc: svc}
}

// GetDeviceUsedDetail 获取设备使用详情
func (h *customerHandler) GetDeviceUsedDetail(c *gin.Context) {
	var (
		req  dto.DeviceUsedDetailRequest
		err  error
		resp *dto.DeviceUsedDetailResponse
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 绑定请求参数
	if err = c.ShouldBindQuery(&req); err != nil {
		err = errors.Wrap(err, "参数错误")
		return
	}

	// 转换为service层实体
	serviceReq := &entity.DeviceUsedDetailRequest{
		Number:  req.Number,
		WeekEnd: req.WeekEnd,
	}

	// 调用服务层
	r, err := h.customerSvc.GetDeviceUsedDetail(c, serviceReq)
	if err != nil {
		return
	}

	// 转换为响应DTO
	resp = &dto.DeviceUsedDetailResponse{
		LastUseTime:          h.formatLastUseTime(r.LastUseTime),
		LastWeekUseAppNums:   r.LastWeekUseAppNums,
		LastWeekUseDuration:  h.formatDuration(r.LastWeekUseDuration),
		UsageTrend:           r.UsageTrend,
		UsagePrefer:          r.UsagePrefer,
		UsageTop10Readboy:    r.UsageTop10Readboy,
		UsageTop10NonReadboy: r.UsageTop10NonReadboy,
	}

	// 处理空的JSON字段
	if len(resp.UsageTrend) == 0 {
		resp.UsageTrend = json.RawMessage("[]")
	}
	if len(resp.UsagePrefer) == 0 {
		resp.UsagePrefer = json.RawMessage("[]")
	}
	if len(resp.UsageTop10Readboy) == 0 {
		resp.UsageTop10Readboy = json.RawMessage("[]")
	}
	if len(resp.UsageTop10NonReadboy) == 0 {
		resp.UsageTop10NonReadboy = json.RawMessage("[]")
	}
}

// formatLastUseTime 格式化最后使用时间
func (h *customerHandler) formatLastUseTime(t *time.Time) *string {
	if t == nil {
		return nil
	}
	formatted := t.Format("2006/01/02 15:04:05")
	return &formatted
}

// formatDuration 转换时间格式（毫秒转为中文显示）
func (h *customerHandler) formatDuration(duration int64) string {
	if duration == 0 {
		return "0时0分"
	}

	// 计量单位毫秒 转为秒
	totalDuration := int(duration / 1000)
	// 再转为分
	totalDurationMinute := totalDuration / 60
	hour := totalDurationMinute / 60
	day := hour / 24
	hour = hour % 24
	minute := totalDurationMinute % 60
	second := totalDuration % 60

	var parts []string
	if day > 0 {
		parts = append(parts, strconv.Itoa(day)+"天")
	}
	if hour > 0 {
		parts = append(parts, strconv.Itoa(hour)+"时")
	}
	if minute > 0 {
		parts = append(parts, strconv.Itoa(minute)+"分")
	}
	if len(parts) == 0 && second > 0 {
		parts = append(parts, strconv.Itoa(second)+"秒")
	}

	if len(parts) == 0 {
		return "0时0分"
	}

	return strings.Join(parts, "")
}

// GetDeviceUsageSummary 获取设备使用总览
func (h *customerHandler) GetDeviceUsageSummary(c *gin.Context) {
	var (
		req  dto.DeviceUsageSummaryRequest
		err  error
		resp *dto.DeviceUsageSummaryResponse
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 绑定请求参数
	if err = c.ShouldBindQuery(&req); err != nil {
		err = errors.Wrap(err, "参数错误")
		return
	}

	// 转换为service层实体
	serviceReq := &entity.DeviceUsageSummaryRequest{
		Number: req.Number,
	}

	// 调用服务层
	r, err := h.customerSvc.GetDeviceUsageSummary(c, serviceReq)
	if err != nil {
		return
	}

	// 转换为响应DTO
	resp = &dto.DeviceUsageSummaryResponse{
		Duration:        r.Duration,
		LastUseDate:     h.formatLastUseDate(r.LastUseDate),
		Apps:            r.Apps,
		UseDays:         r.UseDays,
		AvgDayDuration:  r.AvgDayDuration,
		FavoriteSubject: r.FavoriteSubject,
	}
}

// formatLastUseDate 格式化最后使用时间（Python格式：Y-m-d H:i:s）
func (h *customerHandler) formatLastUseDate(t *time.Time) *string {
	if t == nil {
		return nil
	}
	formatted := t.Format("2006-01-02 15:04:05")
	return &formatted
}

// GetDeviceUsageSummaryBatch 批量获取设备使用总览
func (h *customerHandler) GetDeviceUsageSummaryBatch(c *gin.Context) {
	var (
		req  dto.DeviceUsageSummaryBatchRequest
		err  error
		resp *dto.DeviceUsageSummaryBatchResponse
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 绑定请求参数
	if err = c.ShouldBindQuery(&req); err != nil {
		err = errors.Wrap(err, "参数错误")
		return
	}

	// 解析JSON数组格式的numbers
	var numbers []string
	if err = json.Unmarshal([]byte(req.Numbers), &numbers); err != nil {
		err = errors.Wrap(err, "numbers字段格式错误，应为JSON数组")
		return
	}

	// 转换为service层实体
	serviceReq := &entity.DeviceUsageSummaryBatchRequest{
		Numbers: numbers,
	}

	// 调用服务层
	r, err := h.customerSvc.GetDeviceUsageSummaryBatch(c, serviceReq)
	if err != nil {
		return
	}

	// 转换为响应DTO
	dataMap := make(map[string]dto.DeviceUsageSummaryItem)
	for deviceID, item := range r.Data {
		dataMap[deviceID] = dto.DeviceUsageSummaryItem{
			Duration:        item.Duration,
			LastUseDate:     h.formatLastUseDate(item.LastUseDate),
			Apps:            item.Apps,
			UseDays:         item.UseDays,
			AvgDayDuration:  item.AvgDayDuration,
			FavoriteSubject: item.FavoriteSubject,
		}
	}

	resp = &dto.DeviceUsageSummaryBatchResponse{
		Data: dataMap,
	}
}

// GetTutorUsageSummary 获取教材全解使用总览
func (h *customerHandler) GetTutorUsageSummary(c *gin.Context) {
	var (
		req  dto.TutorUsageSummaryRequest
		err  error
		resp *dto.TutorUsageSummaryResponse
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 绑定请求参数
	if err = c.ShouldBindQuery(&req); err != nil {
		err = errors.Wrap(err, "参数错误")
		return
	}

	// 转换为service层实体
	serviceReq := &entity.TutorUsageSummaryRequest{
		Number:  req.Number,
		WeekEnd: req.WeekEnd,
	}

	// 调用服务层
	r, err := h.customerSvc.GetTutorUsageSummary(c, serviceReq)
	if err != nil {
		return
	}

	// 如果没有数据，返回数据不存在错误
	if r == nil {
		err = errors.New("数据不存在")
		return
	}

	// 转换为响应DTO
	resp = &dto.TutorUsageSummaryResponse{
		Chinese: r.Chinese,
		Math:    r.Math,
		English: r.English,
		Science: r.Science,
	}
}

// GetTutorUsageDetail 获取教材全解使用详情
func (h *customerHandler) GetTutorUsageDetail(c *gin.Context) {
	var (
		req  dto.TutorUsageDetailRequest
		err  error
		resp *dto.TutorUsageDetailResponse
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 绑定请求参数
	if err = c.ShouldBindQuery(&req); err != nil {
		err = errors.Wrap(err, "参数错误")
		return
	}

	// 转换为service层实体
	serviceReq := &entity.TutorUsageDetailRequest{
		Number:  req.Number,
		WeekEnd: req.WeekEnd,
		Subject: req.Subject,
	}

	// 调用服务层
	r, err := h.customerSvc.GetTutorUsageDetail(c, serviceReq)
	if err != nil {
		return
	}

	// 转换为响应DTO
	resp = &dto.TutorUsageDetailResponse{
		Data: r.Data,
	}
}

// GetTutorsplanUsageSummary 获取名师辅导班使用总览
func (h *customerHandler) GetTutorsplanUsageSummary(c *gin.Context) {
	var (
		req  dto.TutorsplanUsageSummaryRequest
		err  error
		resp *dto.TutorsplanUsageSummaryResponse
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 绑定请求参数
	if err = c.ShouldBindQuery(&req); err != nil {
		err = errors.Wrap(err, "参数错误")
		return
	}

	// 转换为service层实体
	serviceReq := &entity.TutorsplanUsageSummaryRequest{
		Number:  req.Number,
		WeekEnd: req.WeekEnd,
	}

	// 调用服务层
	r, err := h.customerSvc.GetTutorsplanUsageSummary(c, serviceReq)
	if err != nil {
		return
	}

	// 如果没有数据，返回数据不存在错误
	if r == nil {
		err = errors.New("数据不存在")
		return
	}

	// 转换为响应DTO
	resp = &dto.TutorsplanUsageSummaryResponse{
		Chinese: r.Chinese,
		Math:    r.Math,
		English: r.English,
		Other:   r.Other,
	}
}

// GetTutorsplanUsageDetail 获取名师辅导班使用详情
func (h *customerHandler) GetTutorsplanUsageDetail(c *gin.Context) {
	var (
		req  dto.TutorsplanUsageDetailRequest
		err  error
		resp *dto.TutorsplanUsageDetailResponse
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 绑定请求参数
	if err = c.ShouldBindQuery(&req); err != nil {
		err = errors.Wrap(err, "参数错误")
		return
	}

	// 转换为service层实体
	serviceReq := &entity.TutorsplanUsageDetailRequest{
		Number:  req.Number,
		WeekEnd: req.WeekEnd,
		Subject: req.Subject,
	}

	// 调用服务层
	r, err := h.customerSvc.GetTutorsplanUsageDetail(c, serviceReq)
	if err != nil {
		return
	}

	// 转换为响应DTO
	resp = &dto.TutorsplanUsageDetailResponse{
		Data: r.Data,
	}
}

// GetDTeacherData 获取双师数据
func (h *customerHandler) GetDTeacherData(c *gin.Context) {
	var (
		req  dto.DTeacherDataRequest
		err  error
		resp *dto.DTeacherDataResponse
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 绑定请求参数
	if err = c.ShouldBindQuery(&req); err != nil {
		err = errors.Wrap(err, "参数错误")
		return
	}

	// 转换为service层实体
	serviceReq := &entity.DTeacherDataRequest{
		UserID: req.UserID,
	}

	// 调用服务层
	r, err := h.customerSvc.GetDTeacherData(c, serviceReq)
	if err != nil {
		return
	}

	// 转换为响应DTO
	resp = &dto.DTeacherDataResponse{
		Data: r.Data,
	}
}

// GetDTeacherReportWeekly 获取双师周报数据
func (h *customerHandler) GetDTeacherReportWeekly(c *gin.Context) {
	var (
		req  dto.DTeacherReportWeeklyRequest
		err  error
		resp *dto.DTeacherReportWeeklyResponse
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 绑定请求参数
	if err = c.ShouldBindQuery(&req); err != nil {
		err = errors.Wrap(err, "参数错误")
		return
	}

	// 转换为service层实体
	serviceReq := &entity.DTeacherReportWeeklyRequest{
		UserID:   req.UserID,
		StartDay: req.StartDay,
	}

	// 调用服务层
	r, err := h.customerSvc.GetDTeacherReportWeekly(c, serviceReq)
	if err != nil {
		return
	}

	// 转换为响应DTO
	resp = &dto.DTeacherReportWeeklyResponse{
		Data: r.Data,
	}
}

// GetDTeacherReportWeeks 获取双师周数据列表
func (h *customerHandler) GetDTeacherReportWeeks(c *gin.Context) {
	var (
		req  dto.DTeacherReportWeeksRequest
		err  error
		resp *dto.DTeacherReportWeeksResponse
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 绑定请求参数
	if err = c.ShouldBindQuery(&req); err != nil {
		err = errors.Wrap(err, "参数错误")
		return
	}

	// 转换为service层实体
	serviceReq := &entity.DTeacherReportWeeksRequest{
		UserID: req.UserID,
	}

	// 调用服务层
	r, err := h.customerSvc.GetDTeacherReportWeeks(c, serviceReq)
	if err != nil {
		return
	}

	// 转换为响应DTO
	resp = &dto.DTeacherReportWeeksResponse{
		Data: r.Data,
	}
}
