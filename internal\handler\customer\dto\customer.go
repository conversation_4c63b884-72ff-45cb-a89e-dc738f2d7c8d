package dto

import "encoding/json"

// DeviceUsedDetailRequest 设备使用详情请求
type DeviceUsedDetailRequest struct {
	Number  string `form:"number" binding:"required"`   // 设备序列号
	WeekEnd string `form:"week_end" binding:"required"` // 周一日期，格式YYYYMMDD
}

// DeviceUsedDetailResponse 设备使用详情响应
type DeviceUsedDetailResponse struct {
	LastUseTime          *string         `json:"last_use_time"`           // 最新使用时间
	LastWeekUseAppNums   int64           `json:"last_week_use_app_nums"`  // 上周使用应用数
	LastWeekUseDuration  string          `json:"last_week_use_duration"`  // 上周使用时长
	UsageTrend           json.RawMessage `json:"usage_trend"`             // 本周使用趋势
	UsagePrefer          json.RawMessage `json:"usage_prefer"`            // 本周使用喜好
	UsageTop10Readboy    json.RawMessage `json:"usage_top10_readboy"`     // 读书郎应用top10
	UsageTop10NonReadboy json.RawMessage `json:"usage_top10_non_readboy"` // 非读书郎应用top10
}

// DeviceUsageSummaryRequest 设备使用总览请求
type DeviceUsageSummaryRequest struct {
	Number string `form:"number" binding:"required"` // 设备序列号
}

// DeviceUsageSummaryResponse 设备使用总览响应
type DeviceUsageSummaryResponse struct {
	Duration        string          `json:"duration"`         // 最后时刻那天的使用时长
	LastUseDate     *string         `json:"last_use_date"`    // 最后使用时刻（格式化后的字符串）
	Apps            json.RawMessage `json:"apps"`             // 经常使用app（JSON数组）
	UseDays         int64           `json:"use_days"`         // 累计使用天数
	AvgDayDuration  int64           `json:"avg_day_duration"` // 平均使用时长
	FavoriteSubject int             `json:"favorite_subject"` // 最喜欢科目 1-语文 2-数学 3-英语
}

// DeviceUsageSummaryBatchRequest 设备使用总览批量请求
type DeviceUsageSummaryBatchRequest struct {
	Numbers string `form:"numbers" binding:"required"` // 设备序列号JSON数组字符串
}

// DeviceUsageSummaryBatchResponse 设备使用总览批量响应
type DeviceUsageSummaryBatchResponse struct {
	Data map[string]DeviceUsageSummaryItem `json:"data"` // 以device_id为key的map结构
}

// DeviceUsageSummaryItem 设备使用总览项目
type DeviceUsageSummaryItem struct {
	Duration        string          `json:"duration"`         // 最后时刻那天的使用时长
	LastUseDate     *string         `json:"last_use_date"`    // 最后使用时刻（格式化后的字符串）
	Apps            json.RawMessage `json:"apps"`             // 经常使用app（JSON数组）
	UseDays         int64           `json:"use_days"`         // 累计使用天数
	AvgDayDuration  int64           `json:"avg_day_duration"` // 平均使用时长
	FavoriteSubject int             `json:"favorite_subject"` // 最喜欢科目 1-语文 2-数学 3-英语
}

// TutorUsageSummaryRequest 教材全解使用总览请求
type TutorUsageSummaryRequest struct {
	Number  string `form:"number" binding:"required"`   // 设备序列号
	WeekEnd string `form:"week_end" binding:"required"` // 周一日期，格式YYYYMMDD
}

// TutorUsageSummaryResponse 教材全解使用总览响应
type TutorUsageSummaryResponse struct {
	Chinese *json.RawMessage `json:"chinese"` // 语文科目数据
	Math    *json.RawMessage `json:"math"`    // 数学科目数据
	English *json.RawMessage `json:"english"` // 英语科目数据
	Science *json.RawMessage `json:"science"` // 科学科目数据
}

// TutorUsageDetailRequest 教材全解使用详情请求
type TutorUsageDetailRequest struct {
	Number  string `form:"number" binding:"required"`   // 设备序列号
	WeekEnd string `form:"week_end" binding:"required"` // 周一日期，格式YYYYMMDD
	Subject int    `form:"subject" binding:"required"`  // 科目类型，1-语文 2-数学 3-英语 4-科学
}

// TutorUsageDetailResponse 教材全解使用详情响应
type TutorUsageDetailResponse struct {
	Data json.RawMessage `json:"data"` // 科目详情数据
}

// TutorsplanUsageSummaryRequest 名师辅导班使用总览请求
type TutorsplanUsageSummaryRequest struct {
	Number  string `form:"number" binding:"required"`   // 设备序列号
	WeekEnd string `form:"week_end" binding:"required"` // 周一日期，格式YYYYMMDD
}

// TutorsplanUsageSummaryResponse 名师辅导班使用总览响应
type TutorsplanUsageSummaryResponse struct {
	Chinese *json.RawMessage `json:"chinese"` // 语文科目数据
	Math    *json.RawMessage `json:"math"`    // 数学科目数据
	English *json.RawMessage `json:"english"` // 英语科目数据
	Other   *json.RawMessage `json:"other"`   // 其他科目数据
}

// TutorsplanUsageDetailRequest 名师辅导班使用详情请求
type TutorsplanUsageDetailRequest struct {
	Number  string `form:"number" binding:"required"`   // 设备序列号
	WeekEnd string `form:"week_end" binding:"required"` // 周一日期，格式YYYYMMDD
	Subject int    `form:"subject" binding:"required"`  // 科目类型，1-语文 2-数学 3-英语 4-其他
}

// TutorsplanUsageDetailResponse 名师辅导班使用详情响应
type TutorsplanUsageDetailResponse struct {
	Data json.RawMessage `json:"data"` // 科目详情数据
}

// DTeacherDataRequest 双师数据请求
type DTeacherDataRequest struct {
	UserID string `form:"userid" binding:"required"` // 用户ID
}

// DTeacherDataResponse 双师数据响应
type DTeacherDataResponse struct {
	Data map[string]interface{} `json:"data"` // 双师数据
}

// DTeacherReportWeeklyRequest 双师周报请求
type DTeacherReportWeeklyRequest struct {
	UserID   int   `form:"user_id" binding:"required"`   // 用户ID
	StartDay int64 `form:"start_day" binding:"required"` // 开始时间戳
}

// DTeacherReportWeeklyResponse 双师周报响应
type DTeacherReportWeeklyResponse struct {
	Data map[string]interface{} `json:"data"` // 双师周报数据
}

// DTeacherReportWeeksRequest 双师周数据列表请求
type DTeacherReportWeeksRequest struct {
	UserID int `form:"user_id" binding:"required"` // 用户ID
}

// DTeacherReportWeeksResponse 双师周数据列表响应
type DTeacherReportWeeksResponse struct {
	Data []interface{} `json:"data"` // 双师周数据列表
}
