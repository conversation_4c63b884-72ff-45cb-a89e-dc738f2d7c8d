package dto

import "time"

// NotificationMessagesReq 获取通知消息请求
type NotificationMessagesReq struct {
	Platform string `form:"platform"  json:"platform"`
	PageSize int    `form:"page_size" binding:"min=0,max=500" json:"page_size"`
	Page     int    `form:"page" binding:"min=0" json:"page"`
	TypeID   uint   `form:"type_id" json:"type_id"`
	Slug     string `form:"slug"  json:"slug"`
}

// NotificationMessage 通知消息响应
type NotificationMessage struct {
	ID             uint64                 `json:"id"`
	NotificationID uint64                 `json:"notification_id"`
	CreatedAt      time.Time              `json:"created_at"`
	Slug           string                 `json:"slug"`
	Read           int8                   `json:"read"`
	Checked        int8                   `json:"checked"`
	Content        map[string]interface{} `json:",inline"` // 内容字段会被展开到根级别
}

// NotificationMessagesResp 获取通知消息响应
type NotificationMessagesResp struct {
	Messages []NotificationMessage `json:"messages"`
}

// NotificationStatusUpdateReq 更新通知状态请求
type NotificationStatusUpdateReq struct {
	IDList string `json:"id_list" binding:"required"` // ID列表，格式如 "1,2,3"
}

// NotificationDetailReq 获取通知详情请求
type NotificationDetailReq struct {
	ID uint64 `uri:"id" binding:"required,min=1"` // 消息ID,是app_notification 表的ID
}

// NotificationDetailResp 获取通知详情响应
type NotificationDetailResp struct {
	ID             uint64                 `json:"id"`
	NotificationID uint64                 `json:"notification_id"`
	CreatedAt      time.Time              `json:"created_at"`
	Slug           string                 `json:"slug"`
	Read           int8                   `json:"read"`
	Checked        int8                   `json:"checked"`
	Content        map[string]interface{} `json:",inline"` // 内容字段会被展开到根级别
}

// NotificationTypeItem 通知类型项
type NotificationTypeItem struct {
	ID          uint   `json:"id"`           // 类型ID
	Name        string `json:"name"`         // 类型名称
	Icon        string `json:"icon"`         // 图标
	Slug        string `json:"slug"`         // 唯一标识
	UnreadCount int64  `json:"unread_count"` // 未读数量
}
