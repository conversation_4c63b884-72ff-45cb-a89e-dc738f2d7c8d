package dto

// TabletUpdateDetailRequest 获取平板更新详情请求
type TabletUpdateDetailRequest struct {
	Params string `uri:"params" binding:"required"` // 加密的参数
	Time   string `form:"time" binding:"required"`  // 时间参数，格式：Y-m-d H:i:s
}

// TabletUpdateItem 平板更新项
type TabletUpdateItem struct {
	ID   uint                   `json:"id"`
	Type string                 `json:"type"`
	Data map[string]interface{} `json:"data"`
}

// TabletUpdateDetailResponse 平板更新详情响应
type TabletUpdateDetailResponse struct {
	Title string                              `json:"title"`
	Type  string                              `json:"type"`
	Time  string                              `json:"time"`
	Rows  map[string][]map[string]interface{} `json:"rows,omitempty"` // 课程类型分组
	Data  []map[string]interface{}            `json:"data,omitempty"` // 应用更新数据
}

// TextBookHistoriesRequest 教材历史记录请求
type TextBookHistoriesRequest struct {
	Type     string `form:"type" binding:"required"` // 类型：sszb_course, msfd_course, paper, homework
	Page     int    `form:"page"`                    // 页码，默认1
	PageSize int    `form:"page_size"`               // 每页大小，默认20
}

// TextBookHistoryItem 教材历史记录项
type TextBookHistoryItem struct {
	Subject interface{} `json:"subject"` // 科目
	Grades  interface{} `json:"grades"`  // 年级
	Name    string      `json:"name"`    // 名称
}

// TextBookHistoriesGroupItem 教材历史记录分组项
type TextBookHistoriesGroupItem struct {
	UpdatedAt string                 `json:"updated_at"` // 更新时间
	Items     []*TextBookHistoryItem `json:"items"`      // 记录项列表
}

// TextBookOptionsResponse 教材选项响应
type TextBookOptionsResponse struct {
	Name  string `json:"name"`  // 显示名称
	Value string `json:"value"` // 值
}
