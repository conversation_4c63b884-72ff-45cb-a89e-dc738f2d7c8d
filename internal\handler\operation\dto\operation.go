package dto

type ArticleCategoryListItem struct {
	ID          uint                      `json:"id"`
	Name        string                    `json:"name"`
	ParentID    uint                      `json:"parent_id"`
	Enabled     uint8                     `json:"enabled"`
	NumArticles uint                      `json:"num_articles"`
	Children    []ArticleCategoryListItem `json:"children,omitempty"`
}

type ArticleCategoryOption struct {
	ID       uint                    `json:"id"`
	Name     string                  `json:"name"`
	ParentID uint                    `json:"parent_id"`
	Children []ArticleCategoryOption `json:"children,omitempty"`
}

type ArticleAttachmentResponse struct {
	Files        []string `json:"files"`
	Covers       []string `json:"covers"`
	Title        string   `json:"title"`
	Desc         string   `json:"desc"`
	Link         string   `json:"link"`
	Downloadable uint8    `json:"downloadable"`
}

type ArticleTag struct {
	ID   uint   `json:"id"`
	Name string `json:"name"`
}

type MarketingCategoryOption struct {
	ID       uint                      `json:"id"`
	Name     string                    `json:"name"`
	ParentID uint                      `json:"parent_id"`
	Children []MarketingCategoryOption `json:"children,omitempty"`
}

// 活动相关DTO
type ActivityListItem struct {
	ID    uint   `json:"id"`
	Title string `json:"title"`
	Type  string `json:"type"`
}

// 销售统计DTO
type SalesStatisticsResponse struct {
	Sales      string `json:"sales"`
	SalesPrev1 string `json:"sales_prev_1"`
	SalesPrev2 string `json:"sales_prev_2"`
}

// 用户资料DTO
type UserProfileResponse struct {
	Blocked uint8 `json:"blocked"`
}

// 分类前端接口DTO
type CategoryDefaultListRequest struct {
	ParentID        uint   `form:"parent_id" binding:"min=0" example:"0" comment:"父分类ID"`
	CustomerVisible *uint8 `form:"customer_visible" binding:"oneof=1" example:"1" comment:"客户可见性"`
}

type CategoryDefaultListItem struct {
	ID          uint   `json:"id"`
	Name        string `json:"name"`
	NumArticles uint   `json:"num_articles"`
}

// 文章前端接口DTO
type ArticleDefaultListRequest struct {
	Page            int    `form:"page" binding:"omitempty,min=1"`
	PageSize        int    `form:"page_size" binding:"omitempty,min=1,max=100"`
	CategoryID      uint   `form:"category_id" binding:"omitempty,min=1"`
	TagID           uint   `form:"tag_id" binding:"omitempty,min=1"`
	CustomerVisible *uint8 `form:"customer_visible" binding:"omitempty,oneof=0 1"`
	Keyword         string `form:"keyword"`
}

type ArticleDefaultListItem struct {
	ID              uint                       `json:"id"`
	Title           string                     `json:"title"`
	Content         string                     `json:"content"`
	Sleight         string                     `json:"sleight"`
	AttachmentType  uint8                      `json:"attachment_type"`
	Attachment      *ArticleAttachmentResponse `json:"attachment,omitempty"`
	Tags            []ArticleTagResponse       `json:"tags"`
	NumShares       uint                       `json:"num_shares"`
	NumComments     uint                       `json:"num_comments"`
	NumViews        uint                       `json:"num_views"`
	NumLikes        uint                       `json:"num_likes"`
	IsLiked         bool                       `json:"is_liked"`
	Creator         string                     `json:"creator"`
	CreatorAvatar   string                     `json:"creator_avatar"`
	Shareable       uint8                      `json:"shareable"`
	WeWorkShareable uint8                      `json:"wework_shareable"`
	CreatedAt       string                     `json:"created_at"`
}

// 文章详情DTO（包含详情页特有字段）
type ArticleDetailItem struct {
	ID              uint                       `json:"id"`
	Title           string                     `json:"title"`
	Content         string                     `json:"content"`
	Sleight         string                     `json:"sleight"`
	AttachmentType  uint8                      `json:"attachment_type"`
	Attachment      *ArticleAttachmentResponse `json:"attachment,omitempty"`
	Tags            []ArticleTagResponse       `json:"tags"`
	NumShares       uint                       `json:"num_shares"`
	NumComments     uint                       `json:"num_comments"`
	NumViews        uint                       `json:"num_views"`
	NumLikes        uint                       `json:"num_likes"`
	IsLiked         bool                       `json:"is_liked"`
	Creator         string                     `json:"creator"`
	CreatorAvatar   string                     `json:"creator_avatar"`
	Shareable       uint8                      `json:"shareable"`
	WeWorkShareable uint8                      `json:"wework_shareable"`
	CommentSetting  uint8                      `json:"comment_setting"`
	CreatedAt       string                     `json:"created_at"`
	UpdatedAt       string                     `json:"updated_at"`
}


// 文章标签DTO
type ArticleTagResponse struct {
	ID   uint   `json:"id"`
	Name string `json:"name"`
}

// 文章搜索DTO
type ArticleSearchRequest struct {
	Keyword string `form:"keyword" binding:"required" example:"数学" comment:"搜索关键词"`
}

type ArticleSearchItem struct {
	ID    uint   `json:"id"`
	Title string `json:"title"`
}

// 文章点赞DTO
type ArticleLikeRequest struct {
	Like *uint8 `json:"like" binding:"required,oneof=0 1" example:"1" comment:"点赞状态，0取消点赞，1点赞"`
}

// 文章浏览DTO
type ArticleViewRequest struct {
	Ids []uint `json:"ids" binding:"required,min=1,dive,min=1" example:"[123,456,789]" comment:"文章ID数组"`
}

// 文章评论DTO
type ArticleCommentListRequest struct {
	Page      int    `form:"page" binding:"min=1" example:"1" comment:"页码"`
	PageSize  int    `form:"page_size" binding:"min=1,max=100" example:"20" comment:"每页数量"`
	ReplySize uint   `form:"reply_size" binding:"required,min=1,max=20" example:"3" comment:"回复数量限制"`
	OrderBy   string `form:"order_by" binding:"omitempty,oneof=created_at num_likes" example:"created_at" comment:"排序字段"`
	Order     string `form:"order" binding:"required_with=OrderBy,oneof=asc desc" example:"desc" comment:"排序方向"`
}

type ArticleCommentCreator struct {
	ID     uint   `json:"id"`
	Name   string `json:"name"`
	Avatar string `json:"avatar"`
}

type ArticleCommentReply struct {
	ID         uint                  `json:"id"`
	Content    string                `json:"content"`
	Attachment []string              `json:"attachment"`
	NumLikes   uint                  `json:"num_likes"`
	Liked      bool                  `json:"liked"`
	Creator    ArticleCommentCreator `json:"creator"`
	CreatedAt  string                `json:"created_at"`
}

type ArticleCommentReplies struct {
	List  []ArticleCommentReply `json:"list"`
	Total uint                  `json:"total"`
}

type ArticleCommentItem struct {
	ID         uint                  `json:"id"`
	Content    string                `json:"content"`
	Attachment []string              `json:"attachment"`
	NumLikes   uint                  `json:"num_likes"`
	Liked      bool                  `json:"liked"`
	Top        uint8                 `json:"top"`
	Discarded  uint8                 `json:"discarded"`
	Creator    ArticleCommentCreator `json:"creator"`
	Replies    ArticleCommentReplies `json:"replies"`
	CreatedAt  string                `json:"created_at"`
}
