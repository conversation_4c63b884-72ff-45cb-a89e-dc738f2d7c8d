package operation

import (
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"marketing-app/internal/handler"
	handlerDto "marketing-app/internal/handler/operation/dto"
	"marketing-app/internal/service/operation"
)

type Operation interface {
	GetActivities(c *gin.Context)
	GetSalesStatistics(c *gin.Context)
	GetUserProfile(c *gin.Context)
	GetArticles(c *gin.Context)
	GetArticleDetail(c *gin.Context)
	GetCategories(c *gin.Context)
	SearchArticles(c *gin.Context)
	LikeArticle(c *gin.Context)
	ViewArticles(c *gin.Context)
	DownloadArticle(c *gin.Context)
	GetArticleComments(c *gin.Context)
}

type operationHandler struct {
	operationSvc operation.OperationSvc
}

func NewOperationHandler(svc operation.OperationSvc) Operation {
	return &operationHandler{operationSvc: svc}
}

// GetActivities 获取活动列表
func (o *operationHandler) GetActivities(c *gin.Context) {
	var (
		err  error
		resp []handlerDto.ActivityListItem
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 获取date参数
	date := c.Query("date")
	if date == "" {
		err = errors.New("date参数不能为空")
		return
	}

	// 获取用户ID
	uid := c.GetUint("uid")
	if gin.Mode() == gin.DebugMode && uid == 0 {
		uid = cast.ToUint(c.Query("uid"))
	}
	if uid == 0 {
		err = errors.New("用户ID不能为空")
		return
	}

	// 调用服务层获取活动列表
	activities, err := o.operationSvc.GetActivities(c, uid, date)
	if err != nil {
		err = errors.Wrap(err, "获取活动列表失败")
		return
	}

	// 转换为响应DTO
	resp = make([]handlerDto.ActivityListItem, len(activities))
	for i, activity := range activities {
		resp[i] = handlerDto.ActivityListItem{
			ID:    activity.ID,
			Title: activity.Title,
			Type:  activity.Type,
		}
	}
}

// GetSalesStatistics 获取销售统计
func (o *operationHandler) GetSalesStatistics(c *gin.Context) {
	var (
		err  error
		resp *handlerDto.SalesStatisticsResponse
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 获取date参数
	date := c.Query("date")
	if date == "" {
		err = errors.New("date参数不能为空")
		return
	}

	// 获取用户ID
	uid := c.GetUint("uid")
	if gin.Mode() == gin.DebugMode && uid == 0 {
		uid = cast.ToUint(c.Query("uid"))
	}
	if uid == 0 {
		err = errors.New("用户ID不能为空")
		return
	}

	// 调用服务层获取销售统计
	statistics, err := o.operationSvc.GetSalesStatistics(c, uid, date)
	if err != nil {
		err = errors.Wrap(err, "获取销售统计失败")
		return
	}

	// 构造响应
	resp = &handlerDto.SalesStatisticsResponse{
		Sales:      statistics.Sales,
		SalesPrev1: statistics.SalesPrev1,
		SalesPrev2: statistics.SalesPrev2,
	}
}

// GetUserProfile 获取用户资料
func (o *operationHandler) GetUserProfile(c *gin.Context) {
	var (
		err  error
		resp *handlerDto.UserProfileResponse
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 获取用户ID
	userID := c.GetUint("uid")
	if gin.Mode() == gin.DebugMode && userID == 0 {
		userID = cast.ToUint(c.Query("uid"))
	}
	if userID == 0 {
		err = errors.New("用户ID不能为空")
		return
	}

	// 调用服务层获取用户资料
	profile, err := o.operationSvc.GetUserProfile(c, userID)
	if err != nil {
		err = errors.Wrap(err, "获取用户资料失败")
		return
	}

	// 构造响应
	resp = &handlerDto.UserProfileResponse{
		Blocked: profile.Blocked,
	}
}

// GetArticles 获取文章列表
func (o *operationHandler) GetArticles(c *gin.Context) {
	var (
		err  error
		resp []*handlerDto.ArticleDefaultListItem
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 解析请求参数
	var params handlerDto.ArticleDefaultListRequest
	if err = c.ShouldBindQuery(&params); err != nil {
		err = errors.Wrap(err, "参数解析失败")
		return
	}

	// 获取用户ID
	uid := c.GetUint("uid")
	if gin.Mode() == gin.DebugMode && uid == 0 {
		uid = cast.ToUint(c.Query("uid"))
	}
	if uid == 0 {
		err = errors.New("用户ID不能为空")
		return
	}

	// 设置默认分页参数
	if params.Page <= 0 {
		params.Page = 1
	}
	if params.PageSize <= 0 {
		params.PageSize = 20
	}

	// 调用服务层获取文章列表
	articles, err := o.operationSvc.GetArticles(c, uid, params.CategoryID, params.TagID, params.CustomerVisible, params.Keyword, params.Page, params.PageSize)
	if err != nil {
		err = errors.Wrap(err, "获取文章列表失败")
		return
	}

	// 转换为响应DTO
	resp = make([]*handlerDto.ArticleDefaultListItem, len(articles))
	for i, article := range articles {
		// 处理附件
		var attachment *handlerDto.ArticleAttachmentResponse
		if article.Attachment != nil {
			attachment = &handlerDto.ArticleAttachmentResponse{
				Files:        article.Attachment.Files,
				Covers:       article.Attachment.Covers,
				Title:        article.Attachment.Title,
				Desc:         article.Attachment.Desc,
				Link:         article.Attachment.Link,
				Downloadable: article.Attachment.Downloadable,
			}
		}

		// 转换标签
		tags := make([]handlerDto.ArticleTagResponse, len(article.Tags))
		for j, tag := range article.Tags {
			tags[j] = handlerDto.ArticleTagResponse{
				ID:   tag.ID,
				Name: tag.Name,
			}
		}

		resp[i] = &handlerDto.ArticleDefaultListItem{
			ID:              article.ID,
			Title:           article.Title,
			Content:         article.Content,
			Sleight:         article.Sleight,
			AttachmentType:  article.AttachmentType,
			Attachment:      attachment,
			Tags:            tags,
			NumShares:       article.NumShares,
			NumComments:     article.NumComments,
			NumViews:        article.NumViews,
			NumLikes:        article.NumLikes,
			IsLiked:         article.IsLiked,
			Creator:         article.Creator,
			CreatorAvatar:   article.CreatorAvatar,
			Shareable:       article.Shareable,
			WeWorkShareable: article.WeWorkShareable,
			CreatedAt:       article.CreatedAt.Format("2006-01-02 15:04:05"),
		}
	}
}

// GetArticleDetail 获取文章详情
func (o *operationHandler) GetArticleDetail(c *gin.Context) {
	var (
		err  error
		resp *handlerDto.ArticleDetailItem
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 获取文章ID参数
	articleIDStr := c.Param("id")
	if articleIDStr == "" {
		err = errors.New("文章ID不能为空")
		return
	}

	articleID := cast.ToUint(articleIDStr)
	if articleID == 0 {
		err = errors.New("文章ID格式错误")
		return
	}

	// 获取用户ID
	uid := c.GetUint("uid")
	if gin.Mode() == gin.DebugMode && uid == 0 {
		uid = cast.ToUint(c.Query("uid"))
	}
	if uid == 0 {
		err = errors.New("用户ID不能为空")
		return
	}

	// 调用服务层获取文章详情
	article, err := o.operationSvc.GetArticleDetail(c, uid, articleID)
	if err != nil {
		err = errors.Wrap(err, "获取文章详情失败")
		return
	}

	if article == nil {
		err = errors.New("文章不存在")
		return
	}

	// 处理附件
	var attachment *handlerDto.ArticleAttachmentResponse
	if article.Attachment != nil {
		attachment = &handlerDto.ArticleAttachmentResponse{
			Files:        article.Attachment.Files,
			Covers:       article.Attachment.Covers,
			Title:        article.Attachment.Title,
			Desc:         article.Attachment.Desc,
			Link:         article.Attachment.Link,
			Downloadable: article.Attachment.Downloadable,
		}
	}

	// 转换标签
	tags := make([]handlerDto.ArticleTagResponse, len(article.Tags))
	for i, tag := range article.Tags {
		tags[i] = handlerDto.ArticleTagResponse{
			ID:   tag.ID,
			Name: tag.Name,
		}
	}

	// 构造响应
	resp = &handlerDto.ArticleDetailItem{
		ID:              article.ID,
		Title:           article.Title,
		Content:         article.Content,
		Sleight:         article.Sleight,
		AttachmentType:  article.AttachmentType,
		Attachment:      attachment,
		Tags:            tags,
		NumShares:       article.NumShares,
		NumComments:     article.NumComments,
		NumViews:        article.NumViews,
		NumLikes:        article.NumLikes,
		IsLiked:         article.IsLiked,
		Creator:         article.Creator,
		CreatorAvatar:   article.CreatorAvatar,
		Shareable:       article.Shareable,
		WeWorkShareable: article.WeWorkShareable,
		CommentSetting:  article.CommentSetting,
		CreatedAt:       article.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:       article.UpdatedAt.Format("2006-01-02 15:04:05"),
	}
}

// GetCategories 获取分类列表
func (o *operationHandler) GetCategories(c *gin.Context) {
	var (
		err  error
		resp []handlerDto.CategoryDefaultListItem
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 解析请求参数
	var params handlerDto.CategoryDefaultListRequest
	if err = c.ShouldBindQuery(&params); err != nil {
		err = errors.Wrap(err, "参数解析失败")
		return
	}

	// 获取用户ID
	uid := c.GetUint("uid")
	if gin.Mode() == gin.DebugMode && uid == 0 {
		uid = cast.ToUint(c.Query("uid"))
	}
	if uid == 0 {
		err = errors.New("用户ID不能为空")
		return
	}

	// 调用服务层获取分类列表
	categories, err := o.operationSvc.GetCategories(c, uid, params.ParentID, params.CustomerVisible)
	if err != nil {
		err = errors.Wrap(err, "获取分类列表失败")
		return
	}

	// 转换为响应DTO
	resp = make([]handlerDto.CategoryDefaultListItem, len(categories))
	for i, category := range categories {
		resp[i] = handlerDto.CategoryDefaultListItem{
			ID:          category.ID,
			Name:        category.Name,
			NumArticles: category.NumArticles,
		}
	}
}

// SearchArticles 搜索文章
func (o *operationHandler) SearchArticles(c *gin.Context) {
	var (
		err  error
		resp []*handlerDto.ArticleSearchItem
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 解析请求参数
	var params handlerDto.ArticleSearchRequest
	if err = c.ShouldBindQuery(&params); err != nil {
		err = errors.Wrap(err, "参数解析失败")
		return
	}

	// 调用服务层搜索文章
	articles, err := o.operationSvc.SearchArticles(c, params.Keyword)
	if err != nil {
		err = errors.Wrap(err, "搜索文章失败")
		return
	}

	// 转换为响应DTO
	resp = make([]*handlerDto.ArticleSearchItem, len(articles))
	for i, article := range articles {
		resp[i] = &handlerDto.ArticleSearchItem{
			ID:    article.ID,
			Title: article.Title,
		}
	}
}

// LikeArticle 点赞或取消点赞文章
func (o *operationHandler) LikeArticle(c *gin.Context) {
	var err error
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, nil)
		}
	}()

	// 获取文章ID参数
	articleIDStr := c.Param("id")
	if articleIDStr == "" {
		err = errors.New("文章ID不能为空")
		return
	}

	articleID := cast.ToUint(articleIDStr)
	if articleID == 0 {
		err = errors.New("文章ID格式错误")
		return
	}

	// 解析请求参数
	var params handlerDto.ArticleLikeRequest
	if err = c.ShouldBindJSON(&params); err != nil {
		err = errors.Wrap(err, "参数解析失败")
		return
	}

	// 获取用户ID
	uid := c.GetUint("uid")
	if gin.Mode() == gin.DebugMode && uid == 0 {
		uid = cast.ToUint(c.Query("uid"))
	}
	if uid == 0 {
		err = errors.New("用户ID不能为空")
		return
	}

	// 调用服务层进行点赞操作
	err = o.operationSvc.LikeArticle(c, uid, articleID, *params.Like)
	if err != nil {
		err = errors.Wrap(err, "点赞操作失败")
		return
	}
}

// ViewArticles 批量浏览文章
func (o *operationHandler) ViewArticles(c *gin.Context) {
	var err error
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, nil)
		}
	}()

	// 解析请求参数
	var params handlerDto.ArticleViewRequest
	if err = c.ShouldBindJSON(&params); err != nil {
		err = errors.Wrap(err, "参数解析失败")
		return
	}

	// 获取用户ID
	uid := c.GetUint("uid")
	if gin.Mode() == gin.DebugMode && uid == 0 {
		uid = cast.ToUint(c.Query("uid"))
	}
	if uid == 0 {
		err = errors.New("用户ID不能为空")
		return
	}

	// 调用服务层记录浏览
	err = o.operationSvc.ViewArticles(c, uid, params.Ids)
	if err != nil {
		err = errors.Wrap(err, "记录文章浏览失败")
		return
	}
}

// DownloadArticle 下载文章
func (o *operationHandler) DownloadArticle(c *gin.Context) {
	var err error
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, nil)
		}
	}()

	// 获取文章ID参数
	articleIDStr := c.Param("id")
	if articleIDStr == "" {
		err = errors.New("文章ID不能为空")
		return
	}

	articleID := cast.ToUint(articleIDStr)
	if articleID == 0 {
		err = errors.New("文章ID格式错误")
		return
	}

	// 获取用户ID
	uid := c.GetUint("uid")
	if gin.Mode() == gin.DebugMode && uid == 0 {
		uid = cast.ToUint(c.Query("uid"))
	}
	if uid == 0 {
		err = errors.New("用户ID不能为空")
		return
	}

	// 调用服务层记录下载
	err = o.operationSvc.DownloadArticle(c, uid, articleID)
	if err != nil {
		err = errors.Wrap(err, "下载文章失败")
		return
	}
}

// GetArticleComments 获取文章评论列表
func (o *operationHandler) GetArticleComments(c *gin.Context) {
	var (
		err  error
		resp []*handlerDto.ArticleCommentItem
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 获取文章ID参数
	articleIDStr := c.Param("id")
	if articleIDStr == "" {
		err = errors.New("文章ID不能为空")
		return
	}

	articleID := cast.ToUint(articleIDStr)
	if articleID == 0 {
		err = errors.New("文章ID格式错误")
		return
	}

	// 解析查询参数
	var params handlerDto.ArticleCommentListRequest
	if err = c.ShouldBindQuery(&params); err != nil {
		err = errors.Wrap(err, "参数解析失败")
		return
	}

	// 设置默认分页参数
	if params.Page <= 0 {
		params.Page = 1
	}
	if params.PageSize <= 0 {
		params.PageSize = 20
	}
	if params.ReplySize == 0 {
		params.ReplySize = 3
	}

	// 获取用户ID
	uid := c.GetUint("uid")
	if gin.Mode() == gin.DebugMode && uid == 0 {
		uid = cast.ToUint(c.Query("uid"))
	}
	if uid == 0 {
		err = errors.New("用户ID不能为空")
		return
	}

	// 调用服务层获取评论列表
	comments, err := o.operationSvc.GetArticleComments(c, uid, articleID, params.Page, params.PageSize, params.ReplySize, params.OrderBy, params.Order)
	if err != nil {
		err = errors.Wrap(err, "获取文章评论列表失败")
		return
	}

	// 转换为Handler层DTO
	resp = make([]*handlerDto.ArticleCommentItem, len(comments))
	for i, comment := range comments {
		// 转换附件
		var attachment []string
		if comment.Attachment != nil {
			if attachSlice, ok := comment.Attachment.([]interface{}); ok {
				attachment = make([]string, len(attachSlice))
				for j, v := range attachSlice {
					if str, ok := v.(string); ok {
						attachment[j] = str
					}
				}
			}
		}
		if attachment == nil {
			attachment = []string{}
		}

		// 转换创建者信息
		var creator handlerDto.ArticleCommentCreator
		if comment.Creator != nil {
			creator = handlerDto.ArticleCommentCreator{
				ID:     comment.Creator.ID,
				Name:   comment.Creator.Name,
				Avatar: comment.Creator.Avatar,
			}
		}

		// 转换回复列表
		var replies handlerDto.ArticleCommentReplies
		if comment.Replies != nil {
			replies.Total = comment.Replies.Total
			replies.List = make([]handlerDto.ArticleCommentReply, len(comment.Replies.List))
			for j, reply := range comment.Replies.List {
				// 转换回复的附件
				var replyAttachment []string
				if reply.Attachment != nil {
					if attachSlice, ok := reply.Attachment.([]interface{}); ok {
						replyAttachment = make([]string, len(attachSlice))
						for k, v := range attachSlice {
							if str, ok := v.(string); ok {
								replyAttachment[k] = str
							}
						}
					}
				}
				if replyAttachment == nil {
					replyAttachment = []string{}
				}

				// 转换回复创建者
				var replyCreator handlerDto.ArticleCommentCreator
				if reply.Creator != nil {
					replyCreator = handlerDto.ArticleCommentCreator{
						ID:     reply.Creator.ID,
						Name:   reply.Creator.Name,
						Avatar: reply.Creator.Avatar,
					}
				}

				replies.List[j] = handlerDto.ArticleCommentReply{
					ID:         reply.ID,
					Content:    reply.Content,
					Attachment: replyAttachment,
					NumLikes:   reply.NumLikes,
					Liked:      reply.IsLiked,
					Creator:    replyCreator,
					CreatedAt:  reply.CreatedAt.Format("2006-01-02 15:04:05"),
				}
			}
		}

		resp[i] = &handlerDto.ArticleCommentItem{
			ID:         comment.ID,
			Content:    comment.Content,
			Attachment: attachment,
			NumLikes:   comment.NumLikes,
			Liked:      comment.IsLiked,
			Top:        comment.Top,
			Discarded:  comment.Discarded,
			Creator:    creator,
			Replies:    replies,
			CreatedAt:  comment.CreatedAt.Format("2006-01-02 15:04:05"),
		}
	}
}
