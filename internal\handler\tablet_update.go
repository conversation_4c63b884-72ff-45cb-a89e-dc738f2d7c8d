package handler

import (
	"marketing-app/internal/handler/dto"
	"marketing-app/internal/service"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
)

type TabletUpdateController interface {
	// Detail 获取平板更新详情
	Detail(c *gin.Context)
}

type tabletUpdateController struct {
	service service.TabletUpdateService
}

func NewTabletUpdateController(service service.TabletUpdateService) TabletUpdateController {
	return &tabletUpdateController{
		service: service,
	}
}

// Detail 获取平板更新详情
// @Summary 获取平板更新详情
// @Description 根据加密参数获取平板更新详情，支持课程上新和软件更新，返回JSON格式数据
// @Tags TabletUpdate
// @Accept json
// @Produce json
// @Param params path string true "加密的ID列表参数（十六进制字符串）"
// @Param time query string true "时间参数，格式：2006-01-02 15:04:05"
// @Success 200 {object} dto.TabletUpdateDetailResponse
// @Failure 400 {object} handler.ErrorResponse
// @Failure 500 {object} handler.ErrorResponse
// @Router /notification/tablet_update/view/{params} [get]
func (h *tabletUpdateController) Detail(c *gin.Context) {
	var (
		req  dto.TabletUpdateDetailRequest
		err  error
		resp *dto.TabletUpdateDetailResponse
	)
	defer func() {
		if err != nil {
			ResponseError(c, err)
		} else {
			ResponseSuccess(c, resp)
		}
	}()

	// 绑定URI参数
	if err = c.ShouldBindUri(&req); err != nil {
		err = errors.Wrap(err, "绑定URI参数失败")
		return
	}

	// 绑定查询参数
	if err = c.ShouldBindQuery(&req); err != nil {
		err = errors.Wrap(err, "绑定查询参数失败")
		return
	}

	// 调用服务层获取详情
	resp, err = h.service.GetTabletUpdateDetail(c, &req)
	if err != nil {
		return
	}
}
