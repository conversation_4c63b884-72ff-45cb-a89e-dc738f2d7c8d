package handler

import (
	"marketing-app/internal/handler/dto"
	"marketing-app/internal/service"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
)

type TabletUpdateController interface {
	// GetTextBookOptions 获取教材选项
	GetTextBookOptions(c *gin.Context)
	// GetTextBookHistories 获取教材历史记录
	GetTextBookHistories(c *gin.Context)
}

type tabletUpdateController struct {
	service service.TabletUpdateService
}

func NewTabletUpdateController(service service.TabletUpdateService) TabletUpdateController {
	return &tabletUpdateController{
		service: service,
	}
}

// Detail 获取平板更新详情
// @Summary 获取平板更新详情
// @Description 根据加密参数获取平板更新详情，支持课程上新和软件更新，返回JSON格式数据
// @Tags TabletUpdate
// @Accept json
// @Produce json
// @Param params path string true "加密的ID列表参数（十六进制字符串）"
// @Param time query string true "时间参数，格式：2006-01-02 15:04:05"
// @Success 200 {object} dto.TabletUpdateDetailResponse
// @Failure 400 {object} handler.ErrorResponse
// @Failure 500 {object} handler.ErrorResponse
// @Router /notification/tablet_update/view/{params} [get]
func (h *tabletUpdateController) Detail(c *gin.Context) {
	var (
		req  dto.TabletUpdateDetailRequest
		err  error
		resp *dto.TabletUpdateDetailResponse
	)
	defer func() {
		if err != nil {
			ResponseError(c, err)
		} else {
			ResponseSuccess(c, resp)
		}
	}()

	// 绑定URI参数
	if err = c.ShouldBindUri(&req); err != nil {
		err = errors.Wrap(err, "绑定URI参数失败")
		return
	}

	// 绑定查询参数
	if err = c.ShouldBindQuery(&req); err != nil {
		err = errors.Wrap(err, "绑定查询参数失败")
		return
	}

	// 调用服务层获取详情
	resp, err = h.service.GetTabletUpdateDetail(c, &req)
	if err != nil {
		return
	}
}

// GetTextBookOptions 获取教材选项
// @Summary 获取教材选项
// @Description 获取教材类型选项列表
// @Tags TabletUpdate
// @Accept json
// @Produce json
// @Success 200 {array} dto.TextBookOptionsResponse
// @Failure 400 {object} handler.ErrorResponse
// @Failure 500 {object} handler.ErrorResponse
// @Router /update-history/default/v1/textbook/resource-options [get]
func (h *tabletUpdateController) GetTextBookOptions(c *gin.Context) {
	var (
		err  error
		resp []dto.TextBookOptionsResponse
	)
	defer func() {
		if err != nil {
			ResponseError(c, err)
		} else {
			ResponseSuccess(c, resp)
		}
	}()

	// 调用服务层获取选项
	resp, err = h.service.GetTextBookOptions(c)
	if err != nil {
		return
	}
}

// GetTextBookHistories 获取教材历史记录
// @Summary 获取教材历史记录
// @Description 根据类型获取教材历史记录，支持分页
// @Tags TabletUpdate
// @Accept json
// @Produce json
// @Param type query string true "类型：sszb_course, msfd_course, paper, homework"
// @Param page query int false "页码，默认1"
// @Param page_size query int false "每页大小，默认20"
// @Success 200 {array} dto.TextBookHistoriesGroupItem
// @Failure 400 {object} handler.ErrorResponse
// @Failure 500 {object} handler.ErrorResponse
// @Router /update-history/default/v1/textbook/histories [get]
func (h *tabletUpdateController) GetTextBookHistories(c *gin.Context) {
	var (
		req  dto.TextBookHistoriesRequest
		err  error
		resp []dto.TextBookHistoriesGroupItem
	)
	defer func() {
		if err != nil {
			ResponseError(c, err)
		} else {
			ResponseSuccess(c, resp)
		}
	}()

	// 绑定查询参数
	if err = c.ShouldBindQuery(&req); err != nil {
		err = errors.Wrap(err, "绑定查询参数失败")
		return
	}

	// 调用服务层获取历史记录
	resp, err = h.service.GetTextBookHistories(c, &req)
	if err != nil {
		return
	}
}
