package middleware

import (
	"errors"
	"marketing-app/internal/model"
	"marketing-app/internal/service"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
)

const (
	ErrUnauthorized     = "未登录"
	ErrTokenExpired     = "登录已经失效，请重新登录"
	ErrPermissionDenied = "没有权限"
	ErrToken            = "非法token"
	BearerPrefix        = "Bearer "
)

// RespBody response body.统一返回响应格式（不确定要不要调用api包的的先放这里吧）
type RespBody struct {
	// http code
	OK int `json:"ok"`
	// response message
	Message string `json:"msg"`
	// response data
	Data any `json:"data,omitempty"`
}

// AuthToken 校验身份
func AuthToken(appSystemService service.AppSystemService, adminUserService service.AdminUserService) gin.HandlerFunc {
	return func(c *gin.Context) {
		systemType := c.GetHeader("x-gate-type")
		if systemType == "" {
			c.JSON(http.StatusBadRequest, RespBody{OK: 0, Message: "系统类型不能为空"})
			c.Abort()
			return
		}
		claims, _, err := parseAndValidateToken(c, systemType, appSystemService, adminUserService)
		if err != nil {
			handleTokenError(c, err)
			return
		}
		// 存到 context，后续 handler 可以直接用
		c.Set("uid", claims.UserID)
		c.Set("username", claims.UserName)
		c.Set("name", claims.Name)
		c.Set("claims", claims)
		c.Next()
	}
}

// CheckAppPermission 校验系统权限
func CheckAppPermission(systemType string, appSystemService service.AppSystemService, adminUserService service.AdminUserService) gin.HandlerFunc {
	return func(c *gin.Context) {
		if gin.Mode() == gin.DebugMode {
			c.Next()
			return
		}
		claims, _, err := parseAndValidateToken(c, systemType, appSystemService, adminUserService)
		if err != nil {
			handleTokenError(c, err)
			return
		}

		if claims.SystemType != systemType {
			c.JSON(http.StatusForbidden, RespBody{OK: 0, Message: "系统类型不匹配"})
			c.Abort()
			return
		}
		c.Next()
	}
}

// 公共方法：解析 + 校验 token
func parseAndValidateToken(c *gin.Context, systemType string,
	appSystemService service.AppSystemService,
	adminUserService service.AdminUserService) (*service.Claims, *model.AppSystemV2, error) {

	token := c.GetHeader("Authorization")
	if token == "" {
		return nil, nil, errors.New(ErrUnauthorized)
	}
	tokenString := strings.TrimPrefix(token, BearerPrefix)

	systemInfo, err := appSystemService.GetAppSystem(c, systemType)
	if err != nil || systemInfo == nil {
		return nil, nil, errors.New("系统不存在或已被禁用")
	}

	claims, err := adminUserService.ValidateToken(tokenString, systemInfo.JwtKey)
	if err != nil {
		return nil, nil, err
	}
	return claims, systemInfo, nil
}

// 错误处理
func handleTokenError(c *gin.Context, err error) {
	switch {
	case errors.Is(err, jwt.ErrTokenExpired):
		c.JSON(http.StatusUnauthorized, RespBody{OK: 0, Message: ErrTokenExpired})
	case err.Error() == ErrUnauthorized:
		c.JSON(http.StatusBadRequest, RespBody{OK: 0, Message: ErrUnauthorized})
	case err.Error() == "系统不存在或已被禁用":
		c.JSON(http.StatusInternalServerError, RespBody{OK: 0, Message: err.Error()})
	default:
		c.JSON(http.StatusForbidden, RespBody{OK: 0, Message: ErrToken})
	}
	c.Abort()
}
