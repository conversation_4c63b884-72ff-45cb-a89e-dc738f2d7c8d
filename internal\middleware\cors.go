package middleware

import (
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

func Cors() gin.HandlerFunc {
	//跨域处理
	config := cors.Config{
		AllowOrigins: []string{"*"},
		AllowMethods: []string{"GET", "POST", "PUT", "PATCH", "DELETE", "HEAD", "OPTIONS"},
		AllowHeaders: []string{
			"Origin",
			"Content-Type",
			"Authorization",
			"X-Requested-With",
			"X-Gate-TypeName",
			"X-Gate-Type",
			"X-Request-ID",
			"Accept",
			"referer",
			"User-Agent",
			"Access-Control-Allow-Origin",
			"Access-Control-Allow-Headers",
		},
		ExposeHeaders:    []string{"Content-Length"},
		AllowCredentials: true,           // 允许携带认证信息
		MaxAge:           12 * time.Hour, // 预检请求结果缓存时间
	}
	return cors.New(config)
}
