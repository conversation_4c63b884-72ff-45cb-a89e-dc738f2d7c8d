package model

import (
	"gorm.io/gorm"
	"time"
)

type Activity struct {
	ID                     uint           `gorm:"primarykey" json:"id"`
	Identity               string         `gorm:"size:11;not null;uniqueIndex:uni_identity;comment:活动唯一标识" json:"identity"`
	EndpointID             uint           `gorm:"not null;comment:举办活动的终端id" json:"endpoint_id"`
	Title                  string         `gorm:"size:50;not null;comment:活动名称" json:"title"`
	Description            string         `gorm:"size:500;not null;comment:活动介绍" json:"description"`
	TypeID                 uint16         `gorm:"not null;comment:活动类型" json:"type_id"`
	CoverImage             string         `gorm:"size:100;not null;default:'';comment:活动封面图片地址" json:"cover_image"`
	StartTime              time.Time      `gorm:"not null;comment:活动开始时间" json:"start_time"`
	EndTime                time.Time      `gorm:"not null;comment:活动结束时间" json:"end_time"`
	Location               string         `gorm:"size:100;not null;default:'';comment:活动地点" json:"location"`
	Enabled                uint8          `gorm:"not null;default:1;comment:是否开启" json:"enabled"`
	DisabledAt             *time.Time     `gorm:"comment:禁用时间" json:"disabled_at"`
	CreatedAt              time.Time      `json:"created_at"`
	UpdatedAt              time.Time      `json:"updated_at"`
	Repeatable             uint8          `gorm:"not null;default:0;comment:活动是否可重复报名" json:"repeatable"`
	ApplicationsLimit      uint16         `gorm:"not null;default:0;comment:活动限制最多多少人限制" json:"applications_limit"`
	ApplicationRequirement string         `gorm:"type:enum('none','purchased');not null;default:'none';comment:报名资格限制，purchased-已购机" json:"application_requirement"`
	Detail                 string         `gorm:"type:text;not null;comment:活动详细信息，json格式" json:"detail"`
	DeletedAt              gorm.DeletedAt `gorm:"index" json:"deleted_at"`
	InterruptedDays        uint8          `gorm:"not null;default:1;comment:学习打卡允许中断天数，0表示不允许中断" json:"interrupted_days"`
}

func (Activity) TableName() string {
	return "activity"
}
