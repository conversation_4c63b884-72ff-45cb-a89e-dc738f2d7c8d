package model

import (
	"time"
)

type ActivityType struct {
	ID                uint16    `gorm:"primarykey" json:"id"`
	Name              string    `gorm:"size:20;not null;comment:类型名称" json:"name"`
	CoverImage        string    `gorm:"size:100;not null;comment:封面图片" json:"cover_image"`
	CreatedAt         time.Time `json:"created_at"`
	UpdatedAt         time.Time `json:"updated_at"`
	Tag               string    `gorm:"type:enum('gift','check_in','learning_exchange','theme','store_meeting','campus_promotion','super_scholar_camp','library','evaluating','online_edu');comment:标签" json:"tag"`
	NumCoexist        uint8     `gorm:"not null;default:1;comment:同一个终端该类型的活动能够同时开启多少个，0表示不限制" json:"num_coexist"`
	PaBanner          string    `gorm:"size:150;not null;comment:家长助手中banner的图片地址" json:"pa_banner"`
	Icon              string    `gorm:"size:150;not null;default:'';comment:图标" json:"icon"`
	PaSettingPosition string    `gorm:"type:enum('banner','service');not null;default:'banner';comment:在家长助手管理设置中的位置" json:"pa_setting_position"`
	Orientation       string    `gorm:"type:enum('customer','endpoint');not null;default:'customer';comment:活动面向的群体" json:"orientation"`
	RegistrationPage  string    `gorm:"size:100;not null;default:'';comment:报名网页url" json:"registration_page"`
}

func (ActivityType) TableName() string {
	return "activity_type"
}
