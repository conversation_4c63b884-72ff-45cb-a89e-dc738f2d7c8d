package model

import (
	"time"
)

// AppNotificationTabletUpdate 平板更新通知表
type AppNotificationTabletUpdate struct {
	ID        uint      `gorm:"primaryKey;autoIncrement;column:id" json:"id"`
	Type      string    `gorm:"column:type;type:varchar(50);not null;comment:更新类型" json:"type"`
	Data      string    `gorm:"column:data;type:text;not null;comment:更新数据JSON" json:"data"`
	CreatedAt time.Time `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
}

// TableName 设置表名
func (AppNotificationTabletUpdate) TableName() string {
	return "app_notification_tablet_update"
}
