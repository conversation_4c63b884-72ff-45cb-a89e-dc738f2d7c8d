package model

import (
	"time"
)

// ClientProfile 用户画像表
type ClientProfile struct {
	DeviceID        string     `gorm:"primaryKey;type:varchar(255);not null;comment:设备序列号" json:"device_id"`
	Duration        string     `gorm:"type:varchar(255);default:null;comment:最后时刻那天的使用时长" json:"duration"`
	LastUseDate     *time.Time `gorm:"type:datetime;default:null;comment:最后使用时刻" json:"last_use_date"`
	Apps            string     `gorm:"type:text;comment:经常使用app" json:"apps"`
	UseDays         int64      `gorm:"type:bigint(255);default:0;comment:累计使用天数" json:"use_days"`
	AvgDayDuration  int64      `gorm:"type:bigint(255);default:0;comment:平均使用时长" json:"avg_day_duration"`
	FavoriteSubject int        `gorm:"type:int(255);default:0;comment:最喜欢科目 1-语文 2-数学 3-英语" json:"favorite_subject"`
}

func (ClientProfile) TableName() string {
	return "client_profile"
}
