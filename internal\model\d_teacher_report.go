package model

import "time"

// DTeacherReport 双师直播周报表
type DTeacherReport struct {
	ID                     int       `gorm:"primaryKey;autoIncrement;column:id" json:"id"`
	UID                    int       `gorm:"type:int(11);comment:学生平板的账户id;column:uid" json:"uid"`
	Weekend                time.Time `gorm:"type:date;comment:周开始日期;column:weekend" json:"weekend"`
	Comment                *string   `gorm:"type:varchar(512);comment:评论;column:comment" json:"comment"`
	LessonList             *string   `gorm:"type:text;comment:课程列表;column:lesson_list" json:"lesson_list"`
	ParticipatedNum        *int      `gorm:"type:int(11);comment:该学生在该周参与过的直播课时总数;column:participated_num" json:"participated_num"`
	ParticipatedTime       *int      `gorm:"type:int(11);comment:该学生在该周观看过的所有直播课时的总观看时长(单位：秒);column:participated_time" json:"participated_time"`
	SubjectsLessonsNumInfo *string   `gorm:"type:text;comment:该学生已报名的课程中在该周各个科目的直播课时数信息;column:subjects_lessons_num_info" json:"subjects_lessons_num_info"`
}

func (DTeacherReport) TableName() string {
	return "d_teacher_report"
}
