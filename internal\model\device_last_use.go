package model

import (
	"time"
)

// DeviceLastUse 设备最新使用情况
type DeviceLastUse struct {
	DeviceID            string     `gorm:"primaryKey;type:varchar(255);not null;comment:设备序列号" json:"device_id"`
	LastUseApp          string     `gorm:"type:text;comment:最近使用应用" json:"last_use_app"`
	LastUsePackage      string     `gorm:"type:text;comment:最近使用应用包" json:"last_use_package"`
	LastUseTime         *time.Time `gorm:"type:datetime;default:null;comment:最近使用时间" json:"last_use_time"`
	LastWeekUseAppNums  *int64     `gorm:"type:bigint(20);default:null;comment:上周使用应用数" json:"last_week_use_app_nums"`
	LastWeekUseDuration *int64     `gorm:"type:bigint(20);default:null;comment:上周使用时长" json:"last_week_use_duration"`
	Weekend             string     `gorm:"type:varchar(255);default:null;comment:周末" json:"weekend"`
}

func (DeviceLastUse) TableName() string {
	return "device_last_use"
}
