package model

// DeviceTop10Use 设备前10使用应用
type DeviceTop10Use struct {
	DeviceID   string `gorm:"primaryKey;type:varchar(255);not null;comment:设备序列号" json:"device_id"`
	Readboy    string `gorm:"type:text;comment:读书郎应用前十" json:"readboy"`
	NonReadboy string `gorm:"type:text;comment:非读书郎应用前十" json:"non_readboy"`
	Weekend    string `gorm:"type:varchar(255);default:null;comment:周末" json:"weekend"`
}

func (DeviceTop10Use) TableName() string {
	return "device_top10_use"
}
