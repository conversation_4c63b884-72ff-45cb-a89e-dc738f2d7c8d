package model

// DeviceUsageHabit 设备使用习惯/偏好
type DeviceUsageHabit struct {
	DeviceID  string `gorm:"primaryKey;type:varchar(255);not null;comment:设备序列号" json:"device_id"`
	WeekUsage string `gorm:"type:text;comment:周使用偏好" json:"week_usage"`
	Weekend   string `gorm:"type:varchar(255);default:null;comment:周末" json:"weekend"`
}

func (DeviceUsageHabit) TableName() string {
	return "device_usage_habit"
}
