package model

// DistributionOfUsage 设备使用分布情况
type DistributionOfUsage struct {
	DeviceID      string `gorm:"primaryKey;type:varchar(255);not null;comment:设备序列号" json:"device_id"`
	WeekUsage     string `gorm:"type:text;comment:周使用情况" json:"week_usage"`
	TotalDuration *int64 `gorm:"type:bigint(255);default:null;comment:周总使用时长" json:"total_duration"`
	Weekend       string `gorm:"type:varchar(255);default:null;comment:周末" json:"weekend"`
}

func (DistributionOfUsage) TableName() string {
	return "distribution_of_usage"
}
