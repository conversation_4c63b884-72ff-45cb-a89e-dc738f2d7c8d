package model

import (
	"time"
)

type MarketingCategory struct {
	ID        uint       `gorm:"primarykey" json:"id"`
	Title     string     `gorm:"size:50;not null;default:'';comment:名称" json:"title"`
	ParentID  uint       `gorm:"not null;default:0;comment:父种类id" json:"parent_id"`
	Order     uint       `gorm:"not null;default:0;comment:排序" json:"order"`
	Route     string     `gorm:"size:20;not null;default:'';comment:树结构完整路径" json:"route"`
	CreatedAt *time.Time `json:"created_at"`
	UpdatedAt time.Time  `json:"updated_at"`
}

func (MarketingCategory) TableName() string {
	return "marketing_category"
}
