package model

import (
	"time"
)

type MarketingDownload struct {
	ID            uint      `gorm:"primarykey" json:"id"`
	Name          string    `gorm:"size:50;not null;default:'';comment:名称" json:"name"`
	Description   string    `gorm:"type:text;not null;comment:描述" json:"description"`
	Preview       string    `gorm:"type:text;not null;comment:预览图" json:"preview"`
	Path          string    `gorm:"type:text;not null;comment:下载路径" json:"path"`
	Category      uint      `gorm:"not null;default:0;comment:分类" json:"category"`
	Status        uint8     `gorm:"not null;default:0;comment:状态，1为通过，0为未通过" json:"status"`
	Top           uint8     `gorm:"not null;default:0;comment:是否置顶，1为置顶，0为未置顶" json:"top"`
	DownloadCount uint      `gorm:"not null;default:0;comment:下载量" json:"download_count"`
	SyncOperation uint8     `gorm:"not null;default:0;comment:是否与运营平台文章同步" json:"sync_operation"`
	OpArticleID   uint      `gorm:"not null;default:0;comment:同步到运营平台的文章id" json:"op_article_id"`
	OpShareable   uint8     `gorm:"not null;default:0;comment:运营平台中是否可分享" json:"op_shareable"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
}

func (MarketingDownload) TableName() string {
	return "marketing_download"
}
