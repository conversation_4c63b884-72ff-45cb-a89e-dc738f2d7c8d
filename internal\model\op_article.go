package model

import (
	"gorm.io/gorm"
	"time"
)

type OpArticle struct {
	ID                  uint           `gorm:"primarykey" json:"id"`
	Title               string         `gorm:"size:100;not null;comment:标题" json:"title"`
	Content             string         `gorm:"type:text;not null;comment:内容" json:"content"`
	Sleight             string         `gorm:"type:text;not null;comment:话术" json:"sleight"`
	AttachmentType      uint8          `gorm:"not null;comment:1-图片，2-视频，3-链接，4-文件" json:"attachment_type"`
	Attachment          string         `gorm:"type:text;not null;comment:附件内容，JSON格式" json:"attachment"`
	CategoryID          uint           `gorm:"not null;comment:分类ID" json:"category_id"`
	Shareable           uint8          `gorm:"not null;comment:是否可分享" json:"shareable"`
	WeworkShareable     uint8          `gorm:"not null;comment:是否可通过企业微信分享" json:"wework_shareable"`
	CommentSetting      uint8          `gorm:"not null;default:2;comment:评论设置，1-无须审核，2-审核可见，3-不可评论" json:"comment_setting"`
	Enabled             uint8          `gorm:"not null;comment:是否发布" json:"enabled"`
	CategoryEnabled     uint8          `gorm:"not null;default:1;comment:所属分类及其祖先分类如果任一禁用，则为0" json:"category_enabled"`
	MarketingDownloadID uint           `gorm:"not null;default:0;comment:与marketing_download表中同步的记录id" json:"marketing_download_id"`
	NumDownloads        uint           `gorm:"not null;default:0;comment:下载数" json:"num_downloads"`
	CreatedBy           uint           `gorm:"not null;comment:创建者ID" json:"created_by"`
	PublisherID         uint           `gorm:"not null;default:0;comment:创建文章时作者选择的运营号" json:"publisher_id"`
	Top                 uint           `gorm:"not null;default:0;comment:是否置顶0-否 1-是" json:"top"`
	CreatedAt           time.Time      `json:"created_at"`
	UpdatedAt           time.Time      `json:"updated_at"`
	DeletedAt           gorm.DeletedAt `gorm:"index" json:"deleted_at"`
}

func (OpArticle) TableName() string {
	return "op_article"
}
