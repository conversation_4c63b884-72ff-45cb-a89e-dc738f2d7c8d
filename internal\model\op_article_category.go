package model

import (
	"gorm.io/gorm"
	"time"
)

type OpArticleCategory struct {
	ID                  uint           `gorm:"primarykey" json:"id"`
	ParentID            uint           `gorm:"default:0;comment:父分类ID" json:"parent_id"`
	Name                string         `gorm:"size:10;not null;comment:分类名称" json:"name"`
	Ancestor            string         `gorm:"size:20;default:'';comment:祖先ID集合，逗号分隔" json:"ancestor"`
	Reserved            uint8          `gorm:"default:0;comment:是否为保留分类" json:"reserved"`
	MarketingCategoryID uint           `gorm:"default:0;comment:marketing_category同步过来的id" json:"marketing_category_id"`
	Order               uint           `gorm:"default:0;comment:排序" json:"order"`
	Enabled             uint8          `gorm:"default:1;comment:是否启用:0=禁用,1=启用" json:"enabled"`
	CreatedBy           uint           `gorm:"not null;comment:创建者ID" json:"created_by"`
	ResourceID          uint           `gorm:"not null;comment:资源组ID，对应resource_groups的ID" json:"resource_id"`
	CreatedAt           time.Time      `json:"created_at"`
	UpdatedAt           time.Time      `json:"updated_at"`
	DeletedAt           gorm.DeletedAt `gorm:"index" json:"deleted_at"`
}

func (OpArticleCategory) TableName() string {
	return "op_article_category"
}
