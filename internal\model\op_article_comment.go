package model

import (
	"database/sql/driver"
	"time"
)

type OpArticleComment struct {
	ID         uint       `gorm:"primarykey" json:"id"`
	Content    string     `gorm:"type:text;not null;comment:评论内容" json:"content"`
	ArticleID  uint       `gorm:"not null;comment:文章ID" json:"article_id"`
	SourceID   uint       `gorm:"not null;default:0;comment:始祖id，文章的评论为0，评论下的评论/回复为所在评论id" json:"source_id"`
	ParentID   uint       `gorm:"not null;default:0;comment:父级id，文章的评论为0，其它为它评论目标的id" json:"parent_id"`
	Ancestors  string     `gorm:"type:text;not null;comment:祖先id合集，逗号分隔" json:"ancestors"`
	ReplyTo    uint       `gorm:"not null;default:0;comment:父id的created_by，文章的评论为0，对哪个用户的评论进行回复" json:"reply_to"`
	Attachment string     `gorm:"type:text;not null;comment:附件内容，JSON格式" json:"attachment"`
	Visible    uint8      `gorm:"not null;default:1;comment:是否可见" json:"visible"`
	Selected   uint8      `gorm:"not null;default:0;comment:是否精选" json:"selected"`
	Top        uint8      `gorm:"not null;default:0;comment:是否置顶" json:"top"`
	Discarded  uint8      `gorm:"not null;default:0;comment:是否放入回收站" json:"discarded"`
	CreatedBy  uint       `gorm:"not null;comment:创建者ID" json:"created_by"`
	CreatedAt  time.Time  `gorm:"default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt  time.Time  `gorm:"default:CURRENT_TIMESTAMP" json:"updated_at"`
	DeletedAt  *time.Time `json:"deleted_at"`
}

func (OpArticleComment) TableName() string {
	return "op_article_comment"
}

// NullTime 自定义可空时间类型
type NullTime struct {
	Time  time.Time
	Valid bool
}

// Scan 实现 Scanner 接口
func (nt *NullTime) Scan(value interface{}) error {
	if value == nil {
		nt.Time, nt.Valid = time.Time{}, false
		return nil
	}
	nt.Valid = true
	if t, ok := value.(time.Time); ok {
		nt.Time = t
	}
	return nil
}

// Value 实现 Valuer 接口
func (nt NullTime) Value() (driver.Value, error) {
	if !nt.Valid {
		return nil, nil
	}
	return nt.Time, nil
}
