package model

import "time"

// OpArticleTag 文章标签表
type OpArticleTag struct {
	ID        uint       `gorm:"primaryKey;autoIncrement;column:id" json:"id"`
	Name      string     `gorm:"type:varchar(255);unique;not null;column:name" json:"name"`
	Reserved  uint       `gorm:"type:tinyint(1);default:0;comment:是否为保留标签;column:reserved" json:"reserved"`
	CreatedBy uint       `gorm:"type:int(11);not null;column:created_by" json:"created_by"`
	CreatedAt time.Time  `gorm:"not null;default:CURRENT_TIMESTAMP;column:created_at" json:"created_at"`
	UpdatedAt time.Time  `gorm:"not null;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;column:updated_at" json:"updated_at"`
	DeletedAt *time.Time `gorm:"column:deleted_at" json:"deleted_at"`
}

func (OpArticleTag) TableName() string {
	return "op_article_tag"
}
