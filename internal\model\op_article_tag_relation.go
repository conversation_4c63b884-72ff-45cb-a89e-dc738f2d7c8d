package model

// OpArticleTagRelation 文章标签关系表
type OpArticleTagRelation struct {
	ID        uint `gorm:"primaryKey;autoIncrement;column:id" json:"id"`
	ArticleID uint `gorm:"type:int(10) unsigned;not null;column:article_id;uniqueIndex:article_id,priority:1" json:"article_id"`
	TagID     uint `gorm:"type:int(10) unsigned;not null;column:tag_id;uniqueIndex:article_id,priority:2" json:"tag_id"`
}

func (OpArticleTagRelation) TableName() string {
	return "op_article_tag_relation"
}
