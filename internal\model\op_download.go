package model

import "time"

type OpDownload struct {
	ID        uint      `gorm:"primarykey" json:"id"`
	Target    uint8     `gorm:"not null;comment:下载目标，1-文章" json:"target"`
	TargetID  uint      `gorm:"not null;comment:下载目标的id" json:"target_id"`
	UserID    uint      `gorm:"not null" json:"user_id"`
	CreatedAt time.Time `gorm:"not null;default:CURRENT_TIMESTAMP" json:"created_at"`
}

func (OpDownload) TableName() string {
	return "op_download"
}