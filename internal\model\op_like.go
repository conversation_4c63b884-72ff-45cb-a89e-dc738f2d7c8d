package model

import "time"

type OpLike struct {
	ID        uint      `gorm:"primarykey" json:"id"`
	Target    uint8     `gorm:"not null;comment:点赞目标，1-文章，2-评论" json:"target"`
	TargetID  uint      `gorm:"not null;comment:点赞目标的id" json:"target_id"`
	UserID    uint      `gorm:"not null;uniqueIndex:idx_user_target;comment:用户ID" json:"user_id"`
	CreatedAt time.Time `gorm:"default:CURRENT_TIMESTAMP" json:"created_at"`
}

func (OpLike) TableName() string {
	return "op_like"
}
