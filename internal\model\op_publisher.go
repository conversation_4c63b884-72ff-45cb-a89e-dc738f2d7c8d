package model

import (
	"gorm.io/gorm"
	"time"
)

type OpPublisher struct {
	ID        uint           `gorm:"primarykey" json:"id"`
	Name      string         `gorm:"size:20;not null;comment:运营号名称" json:"name"`
	Avatar    string         `gorm:"size:120;not null;comment:头像" json:"avatar"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at"`
}

func (OpPublisher) TableName() string {
	return "op_publisher"
}
