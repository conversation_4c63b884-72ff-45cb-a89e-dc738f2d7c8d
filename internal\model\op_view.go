package model

import (
	"time"
)

type OpView struct {
	ID        uint      `gorm:"primarykey" json:"id"`
	Target    uint8     `gorm:"not null;comment:浏览目标，1-文章" json:"target"`
	TargetID  uint      `gorm:"not null;comment:浏览目标的id" json:"target_id"`
	UserID    uint      `gorm:"not null" json:"user_id"`
	CreatedAt time.Time `gorm:"not null;default:CURRENT_TIMESTAMP" json:"created_at"`
}

func (OpView) TableName() string {
	return "op_view"
}
