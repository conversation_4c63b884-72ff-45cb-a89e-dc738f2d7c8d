package model

import (
	"time"
)

type ShareInfo struct {
	ID        uint      `gorm:"primarykey" json:"id"`
	Object    string    `gorm:"size:20;not null" json:"object"`
	ObjectID  uint      `gorm:"not null;default:0;comment:分享对象的id，如果为0表示无" json:"object_id"`
	ObjectURL string    `gorm:"size:150;not null;default:'';comment:object为url时保存url的值" json:"object_url"`
	Medium    string    `gorm:"type:enum('link','qrcode','poster','text','wechat_id','call');not null;comment:分享的形式，link-链接，qrcode-二维码，poster-海报，text-文本" json:"medium"`
	CreatedAt time.Time `gorm:"not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	SIDKey    string    `gorm:"size:15;not null;default:'_sid';comment:sid这个参数在url上的参数名" json:"sid_key"`
}

func (ShareInfo) TableName() string {
	return "share_info"
}
