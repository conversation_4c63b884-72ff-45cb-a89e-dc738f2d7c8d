package model

import (
	"time"
)

type ShareLog struct {
	ID         uint      `gorm:"primarykey" json:"id"`
	UserID     uint      `gorm:"not null;comment:分享用户id" json:"user_id"`
	ShareID    uint      `gorm:"not null;comment:分享对象id" json:"share_id"`
	CreatedAt  time.Time `gorm:"not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	Shared     uint8     `gorm:"not null;default:0;comment:这个分享对象是否真的被分享出去了" json:"shared"`
	EndpointID uint      `gorm:"not null;default:0;comment:用户分享时所在终端，因为用户可能更换终端，所以要记录下来" json:"endpoint_id"`
}

func (ShareLog) TableName() string {
	return "share_log"
}
