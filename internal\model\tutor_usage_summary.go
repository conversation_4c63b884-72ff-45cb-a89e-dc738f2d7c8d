package model

// TutorUsageSummary 教材全解总览
type TutorUsageSummary struct {
	DeviceID string `gorm:"primaryKey;type:varchar(60);not null;comment:设备序列号" json:"device_id"`
	Chinese  string `gorm:"type:text;comment:语文" json:"chinese"`
	Math     string `gorm:"type:text;comment:数学" json:"math"`
	English  string `gorm:"type:text;comment:英语" json:"english"`
	Science  string `gorm:"type:text;comment:科学" json:"science"`
	Weekend  string `gorm:"type:varchar(10);default:null;comment:周末" json:"weekend"`
}

// TableName 指定表名
func (TutorUsageSummary) TableName() string {
	return "tutor_usage_summary"
}
