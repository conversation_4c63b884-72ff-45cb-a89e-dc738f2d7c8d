package model

// TutorsplanUsageSummary 名师辅导班总览
type TutorsplanUsageSummary struct {
	DeviceID string `gorm:"primaryKey;type:varchar(60);not null;comment:设备序列号" json:"device_id"`
	Chinese  string `gorm:"type:text;comment:语文" json:"chinese"`
	Math     string `gorm:"type:text;comment:数学" json:"math"`
	English  string `gorm:"type:text;comment:英语" json:"english"`
	Other    string `gorm:"type:text;comment:其他" json:"other"`
	Total    string `gorm:"type:text;comment:汇总" json:"total"`
	Weekend  string `gorm:"type:varchar(10);default:null;comment:周末" json:"weekend"`
}

// TableName 指定表名
func (TutorsplanUsageSummary) TableName() string {
	return "tutorsplan_usage_summary"
}
