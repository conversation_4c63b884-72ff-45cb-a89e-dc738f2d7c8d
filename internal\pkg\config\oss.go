package config

// OSSConfig OSS配置结构体
type OSSConfig struct {
	AccessID  string `mapstructure:"access_id"`
	AccessKey string `mapstructure:"access_key"`
	Endpoint  string `mapstructure:"endpoint"`
	Bucket    string `mapstructure:"bucket"`
	CName     string `mapstructure:"cname"`
}

// GetOSSConfig 获取OSS配置
func GetOSSConfig() (*OSSConfig, error) {
	var ossConfig OSSConfig
	err := UnmarshalKey("oss", &ossConfig)
	if err != nil {
		return nil, err
	}
	ossConfig.AccessID = GetString("oss.access.key.id")
	ossConfig.AccessKey = GetString("oss.access.key.secret")
	return &ossConfig, nil
}
