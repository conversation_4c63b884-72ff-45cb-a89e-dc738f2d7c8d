package log

import (
	"os"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// Level 日志级别类型
type Level string

// 定义日志级别
const (
	DebugLevel Level = "debug"
	InfoLevel  Level = "info"
	WarnLevel  Level = "warn"
	ErrorLevel Level = "error"
)

// Config 日志配置
type Config struct {
	Level      Level  // 日志级别
	TimeFormat string // 时间格式，默认"2006-01-02T15:04:05.000Z07:00"
}

var globalLogger *zap.Logger

// Init 初始化日志
func Init(cfg Config) error {
	// 设置日志级别
	var level zapcore.Level
	switch cfg.Level {
	case DebugLevel:
		level = zap.DebugLevel
	case WarnLevel:
		level = zap.WarnLevel
	case ErrorLevel:
		level = zap.ErrorLevel
	default:
		level = zap.InfoLevel
	}
	// 配置编码器
	encoderConfig := zapcore.EncoderConfig{
		TimeKey:        "time",
		LevelKey:       "level",
		NameK<PERSON>:        "logger",
		Caller<PERSON>ey:      "caller",
		FunctionKey:    zapcore.OmitKey,
		MessageKey:     "msg",
		StacktraceKey:  "stacktrace",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    zapcore.CapitalLevelEncoder, // 级别大写（INFO, ERROR）
		EncodeTime:     customTimeEncoder(cfg.TimeFormat),
		EncodeDuration: zapcore.StringDurationEncoder,
		EncodeCaller:   zapcore.ShortCallerEncoder, // 短路径调用者（如logger/logger.go:42）
	}

	// 输出到控制台
	writeSyncer := zapcore.AddSync(os.Stdout)

	// 构建核心
	zc := zapcore.NewJSONEncoder(encoderConfig)
	if gin.Mode() == gin.DebugMode {
		zc = zapcore.NewConsoleEncoder(encoderConfig)
	}
	core := zapcore.NewCore(
		zc,
		writeSyncer,
		level,
	)

	// 开发模式添加调用者和栈跟踪
	var options []zap.Option
	if cfg.Level == DebugLevel {
		options = append(options,
			zap.Development(),
			zap.AddCaller(),
			zap.AddStacktrace(zap.ErrorLevel),
		)
	}

	// 创建全局日志实例
	globalLogger = zap.New(core, options...)
	return nil
}

// 自定义时间编码器
func customTimeEncoder(timeFormat string) zapcore.TimeEncoder {
	if timeFormat == "" {
		return zapcore.RFC3339NanoTimeEncoder // 默认使用RFC3339带纳秒格式
	}
	return func(t time.Time, enc zapcore.PrimitiveArrayEncoder) {
		enc.AppendString(t.Format(timeFormat))
	}
}

func Debug(msg string, fields ...zap.Field) {
	globalLogger.Debug(msg, fields...)
}

func Info(msg string, fields ...zap.Field) {
	globalLogger.Info(msg, fields...)
}

func Warn(msg string, fields ...zap.Field) {
	globalLogger.Warn(msg, fields...)
}

func Error(msg string, fields ...zap.Field) {
	globalLogger.Error(msg, fields...)
}

func Panic(msg string, fields ...zap.Field) {
	globalLogger.Panic(msg, fields...)
}

func Fatal(msg string, fields ...zap.Field) {
	globalLogger.Fatal(msg, fields...)
}

// With 创建带固定字段的日志实例
func With(fields ...zap.Field) *zap.Logger {
	return globalLogger.With(fields...)
}

// Sync 刷新日志到输出（程序退出前调用）
func Sync() error {
	return globalLogger.Sync()
}
