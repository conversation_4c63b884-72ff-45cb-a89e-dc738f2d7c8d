package oss

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha1"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"marketing-app/internal/pkg/config"
	"marketing-app/internal/pkg/log"
	"math/rand"
	"strings"
	"time"

	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"github.com/pkg/errors"
	"go.uber.org/zap"
)

// OSSService OSS服务接口
type OSSService interface {
	UploadFile(filename string, data []byte) error
	GetFileInfo(filename string) (*FileInfo, error)
	GetFullURL(filename string) string
	GetUploadParams(keyPrefix string) (*UploadParams, error)
}

// FileInfo 文件信息
type FileInfo struct {
	Key          string
	ETag         string
	ContentType  string
	Size         int64
	LastModified time.Time
}

// UploadParams OSS上传参数
type UploadParams struct {
	AccessKeyID string `json:"access_key_id"`
	Host        string `json:"host"`
	Policy      string `json:"policy"`
	Signature   string `json:"signature"`
	Expire      int64  `json:"expire"`
	Key         string `json:"key"`
	BaseURL     string `json:"base_url"`
}

// ossServiceImpl OSS服务实现
type ossServiceImpl struct {
	bucket *oss.Bucket
	config *config.OSSConfig
}

// NewOSSService 创建OSS服务实例
func NewOSSService() (OSSService, error) {
	ossConfig, err := config.GetOSSConfig()
	if err != nil {
		return nil, errors.Wrap(err, "获取OSS配置失败")
	}

	// 创建OSSClient实例
	client, err := oss.New(ossConfig.Endpoint, ossConfig.AccessID, ossConfig.AccessKey)
	if err != nil {
		return nil, errors.Wrap(err, "创建OSS客户端失败")
	}

	// 获取存储空间
	bucket, err := client.Bucket(ossConfig.Bucket)
	if err != nil {
		return nil, errors.Wrap(err, "获取OSS存储空间失败")
	}

	return &ossServiceImpl{
		bucket: bucket,
		config: ossConfig,
	}, nil
}

// UploadFile 上传文件到OSS
func (o *ossServiceImpl) UploadFile(filename string, data []byte) error {
	reader := bytes.NewReader(data)
	err := o.bucket.PutObject(filename, reader)
	if err != nil {
		return errors.Wrap(err, "上传文件到OSS失败")
	}

	log.Info("文件上传成功", zap.String("filename", filename))
	return nil
}

// GetFileInfo 获取文件信息
func (o *ossServiceImpl) GetFileInfo(filename string) (*FileInfo, error) {
	head, err := o.bucket.GetObjectDetailedMeta(filename)
	if err != nil {
		return nil, errors.Wrap(err, "获取文件信息失败")
	}

	// 构建FileInfo
	fileInfo := &FileInfo{
		Key:          filename,
		ETag:         head.Get("ETag"),
		ContentType:  head.Get("Content-Type"),
		Size:         0,
		LastModified: time.Time{},
	}

	// 解析Content-Length
	if contentLength := head.Get("Content-Length"); contentLength != "" {
		if _, err := fmt.Sscanf(contentLength, "%d", &fileInfo.Size); err != nil {
			log.Warn("解析Content-Length失败", zap.String("contentLength", contentLength), zap.Error(err))
		}
	}

	return fileInfo, nil
}

// GetFullURL 获取完整的文件访问URL
func (o *ossServiceImpl) GetFullURL(filename string) string {
	return o.config.CName + filename
}

// GetUploadParams 获取OSS上传参数（用于前端直传）
func (o *ossServiceImpl) GetUploadParams(keyPrefix string) (*UploadParams, error) {
	// 设置30秒过期时间
	expiration := time.Now().Add(30 * time.Second)
	expirationStr := expiration.Format("2006-01-02T15:04:05.000Z")

	// 生成随机文件名
	randomKey := generateRandomKey(16)
	key := keyPrefix + randomKey

	// 构建上传策略
	policy := map[string]interface{}{
		"expiration": expirationStr,
		"conditions": []interface{}{
			[]interface{}{"content-length-range", 0, 2097152000}, // 2GB限制
			[]interface{}{"starts-with", "$key", keyPrefix},
		},
	}

	// 编码policy
	policyBytes, err := json.Marshal(policy)
	if err != nil {
		return nil, errors.Wrap(err, "编码policy失败")
	}

	encodedPolicy := base64.StdEncoding.EncodeToString(policyBytes)

	// 生成签名
	signature := o.generateSignature(encodedPolicy)

	// 构建主机地址
	host := fmt.Sprintf("https://%s.%s", o.config.Bucket,
		o.config.Endpoint[strings.LastIndex(o.config.Endpoint, "/")+1:])

	return &UploadParams{
		AccessKeyID: o.config.AccessID,
		Host:        host,
		Policy:      encodedPolicy,
		Signature:   signature,
		Expire:      expiration.Unix(),
		Key:         key,
		BaseURL:     o.config.CName,
	}, nil
}

// generateSignature 生成HMAC-SHA1签名
func (o *ossServiceImpl) generateSignature(encodedPolicy string) string {
	h := hmac.New(sha1.New, []byte(o.config.AccessKey))
	h.Write([]byte(encodedPolicy))
	return base64.StdEncoding.EncodeToString(h.Sum(nil))
}

// generateRandomKey 生成随机字符串
func generateRandomKey(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	result := make([]byte, length)
	for i := range result {
		result[i] = charset[rand.Intn(len(charset))]
	}
	return string(result)
}
