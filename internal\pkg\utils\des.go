package utils

import (
	"crypto/cipher"
	"crypto/des"
	"encoding/hex"
	"errors"
)

// DESCrypto DES加密解密工具
type DESCrypto struct {
	key []byte
	iv  []byte
}

// NewDESCrypto 创建DES加密解密实例
func NewDESCrypto(key, iv string) *DESCrypto {
	// key和iv必须是8字节长
	if len(key) != 8 || len(iv) != 8 {
		return nil
	}
	return &DESCrypto{
		key: []byte(key),
		iv:  []byte(iv),
	}
}

// Encrypt 加密数据并返回十六进制字符串
func (d *DESCrypto) Encrypt(plaintext string) (string, error) {
	block, err := des.NewCipher(d.key)
	if err != nil {
		return "", err
	}

	// 使用CBC模式
	mode := cipher.NewCBCEncrypter(block, d.iv)

	// PKCS5填充
	data := []byte(plaintext)
	data = d.pkcs5Padding(data, block.BlockSize())

	// 加密
	encrypted := make([]byte, len(data))
	mode.CryptBlocks(encrypted, data)

	// 转换为十六进制字符串
	return hex.EncodeToString(encrypted), nil
}

// Decrypt 解密十六进制字符串
func (d *DESCrypto) Decrypt(hexStr string) (string, error) {
	// 检查是否为有效的十六进制字符串
	if !IsHexString(hexStr) {
		return "", errors.New("invalid hex string")
	}

	// 将十六进制字符串转换为字节数组
	encrypted, err := hex.DecodeString(hexStr)
	if err != nil {
		return "", err
	}

	block, err := des.NewCipher(d.key)
	if err != nil {
		return "", err
	}

	// 使用CBC模式
	mode := cipher.NewCBCDecrypter(block, d.iv)

	// 解密
	decrypted := make([]byte, len(encrypted))
	mode.CryptBlocks(decrypted, encrypted)

	// 去除PKCS5填充
	decrypted, err = d.pkcs5UnPadding(decrypted)
	if err != nil {
		return "", err
	}

	return string(decrypted), nil
}

// pkcs5Padding PKCS5填充
func (d *DESCrypto) pkcs5Padding(data []byte, blockSize int) []byte {
	padding := blockSize - len(data)%blockSize
	padtext := make([]byte, padding)
	for i := range padtext {
		padtext[i] = byte(padding)
	}
	return append(data, padtext...)
}

// pkcs5UnPadding 去除PKCS5填充
func (d *DESCrypto) pkcs5UnPadding(data []byte) ([]byte, error) {
	length := len(data)
	if length == 0 {
		return nil, errors.New("invalid padding")
	}

	unpadding := int(data[length-1])
	if unpadding > length {
		return nil, errors.New("invalid padding")
	}

	return data[:(length - unpadding)], nil
}

// IsHexString 检查字符串是否为有效的十六进制字符串
func IsHexString(s string) bool {
	if len(s)%2 != 0 {
		return false
	}
	for _, c := range s {
		if !((c >= '0' && c <= '9') || (c >= 'a' && c <= 'f') || (c >= 'A' && c <= 'F')) {
			return false
		}
	}
	return true
}
