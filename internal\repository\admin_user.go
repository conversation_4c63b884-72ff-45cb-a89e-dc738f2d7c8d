package repository

import (
	"marketing-app/internal/model"
	"marketing-app/internal/repository/dto"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"gorm.io/gorm"
)

type AdminUserRepository interface {
	// GetAdminUserByID 根据ID获取管理员用户信息
	GetAdminUserByID(c *gin.Context, id int64) (*model.AdminUser, error)
	// GetAdminUserByUsername 根据用户名获取管理员用户信息
	GetAdminUserByUsername(c *gin.Context, username string) (*model.AdminUser, error)
	// GetAdminUserByPhone 根据手机号获取管理员用户信息
	GetAdminUserByPhone(c *gin.Context, phone string) (*model.AdminUser, error)
	// GetByQwUserID 根据企业微信用户ID获取管理员用户信息
	GetByQwUserID(c *gin.Context, QwUserID string) (*model.AdminUser, error)
	//	UpdateUserActiveTime 更新用户活跃时间
	UpdateUserActiveTime(c *gin.Context, id uint) error
	// CheckRole 校验用户角色
	CheckRole(c *gin.Context, uid int, appType int) (roles []*dto.Role, err error)
	// GetUserEndpoint 获取用户终端
	GetUserEndpoint(c *gin.Context, uid uint) (*model.Endpoint, error)
	// GetUserResourceID 获取用户资源ID
	GetUserResourceID(c *gin.Context, uid uint) (uint, error)
	// GetEndpointResourceID 获取终端资源ID
	GetEndpointResourceID(c *gin.Context, endpointID uint) (uint, error)
	// ContainRoles 检查用户是否有指定角色中的任一个
	ContainRoles(c *gin.Context, uid uint, roleNames ...string) (bool, error)
	// GetUserAgency 获取用户代理信息（根据角色决定获取TopAgency还是SecondAgency）
	GetUserAgency(c *gin.Context, uid uint) (*model.Agency, error)
	// GetAgencyResourceID 获取代理的ResourceID（支持父级代理的ResourceID）
	GetAgencyResourceID(c *gin.Context, agencyID uint) (uint, error)
}

type adminUserRepo struct {
	db *gorm.DB //
}

func NewAdminUserRepository(db *gorm.DB) AdminUserRepository {
	return &adminUserRepo{db: db}
}

func (r *adminUserRepo) GetAdminUserByID(c *gin.Context, id int64) (*model.AdminUser, error) {
	var user model.AdminUser
	if err := r.db.WithContext(c).Where("id = ?", id).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil // 用户不存在
		}
		return nil, err
	}
	return &user, nil
}

func (r *adminUserRepo) GetAdminUserByUsername(c *gin.Context, username string) (*model.AdminUser, error) {
	var user model.AdminUser
	if err := r.db.WithContext(c).Where("username = ?", username).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil // 用户不存在
		}
		return nil, err
	}
	return &user, nil
}

func (r *adminUserRepo) GetAdminUserByPhone(c *gin.Context, phone string) (*model.AdminUser, error) {
	var user model.AdminUser
	if err := r.db.WithContext(c).Where("phone = ?", phone).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil // 用户不存在
		}
		return nil, err
	}
	return &user, nil
}

func (r *adminUserRepo) GetByQwUserID(c *gin.Context, QwUserID string) (*model.AdminUser, error) {
	var user model.AdminUser
	if err := r.db.WithContext(c).Where("qw_userid = ?", QwUserID).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil // 用户不存在
		}
		return nil, err // 其他错误
	}
	return &user, nil
}

// UpdateUserActiveTime 更新用户活跃状态
func (r *adminUserRepo) UpdateUserActiveTime(c *gin.Context, id uint) error {
	return r.db.WithContext(c).Model(&model.AdminUser{}).Where("id = ?", id).Update("actived_at", time.Now().Format(time.DateTime)).Error
}

func (r *adminUserRepo) CheckRole(c *gin.Context, uid int, appType int) (roles []*dto.Role, err error) {
	if appType == -1 {
		err = r.db.WithContext(c).Table("admin_users").Select("r.id, r.slug, r.name").
			Joins("RIGHT JOIN admin_role_users ru on admin_users.id=ru.user_id").
			Joins("LEFT JOIN admin_roles r on ru.role_id=r.id").
			Where("admin_users.id = ?", uid).
			Scan(&roles).Error
	} else {
		err = r.db.WithContext(c).Table("admin_users").Select("r.id, r.slug, r.name").
			Joins("RIGHT JOIN admin_role_users ru on admin_users.id=ru.user_id").
			Joins("LEFT JOIN admin_roles r on ru.role_id=r.id").
			Where("admin_users.id = ?", uid).
			Where("r.app_type = ? or r.app_type = 100", appType).
			Scan(&roles).Error
	}
	return roles, err
}

func (r *adminUserRepo) GetUserEndpoint(c *gin.Context, uid uint) (*model.Endpoint, error) {
	var endpoint model.Endpoint
	err := r.db.WithContext(c).
		Table("user_endpoint ue").
		Select("e.id, e.name, e.type, e.top_agency").
		Joins("INNER JOIN endpoint e ON ue.endpoint = e.id").
		Where("ue.uid = ?", uid).
		Take(&endpoint).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return &endpoint, nil
}

// GetUserResourceID 获取用户资源ID（实现原项目完整逻辑）
func (r *adminUserRepo) GetUserResourceID(c *gin.Context, uid uint) (uint, error) {
	type userResourceResult struct {
		ResourceID uint `json:"resource_id"`
	}

	// 1. 首先从用户表直接获取resource_id
	var userResult userResourceResult
	err := r.db.WithContext(c).
		Table("admin_users").
		Select("resource_id").
		Where("id = ?", uid).
		Take(&userResult).Error

	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return 0, errors.Wrap(err, "查询用户资源ID失败")
	}

	var resourceID = userResult.ResourceID

	// 2. 如果用户有TopAgency/TopAgencyAssistant/SecondAgency角色，从Agency获取ResourceId
	hasAgencyRole, err := r.ContainRoles(c, uid, "topAgency", "topAgencyAssistant", "secondAgency")
	if err != nil {
		return 0, errors.Wrap(err, "检查用户代理角色失败")
	}

	if hasAgencyRole && resourceID == 0 {
		// 获取用户代理信息
		agency, err := r.GetUserAgency(c, uid)
		if err != nil {
			return 0, errors.Wrap(err, "获取用户代理信息失败")
		}

		if agency != nil {
			// 获取代理的ResourceID
			agencyResourceID, err := r.GetAgencyResourceID(c, uint(agency.ID))
			if err != nil {
				return 0, errors.Wrap(err, "获取代理资源ID失败")
			}
			if agencyResourceID > 0 {
				resourceID = agencyResourceID
			}
		}
	}

	// 3. 如果用户没有EndpointVisitor角色，从Endpoint获取ResourceId
	hasEndpointVisitorRole, err := r.ContainRoles(c, uid, "endpointVisitor")
	if err != nil {
		return 0, errors.Wrap(err, "检查用户终端访客角色失败")
	}

	if !hasEndpointVisitorRole && resourceID == 0 {
		// 获取用户终端信息
		endpoint, err := r.GetUserEndpoint(c, uid)
		if err != nil {
			return 0, err
		}

		if endpoint != nil {
			endpointResourceID, err := r.GetEndpointResourceID(c, uint(endpoint.ID))
			if err != nil {
				return 0, err
			}
			if endpointResourceID > 0 {
				resourceID = endpointResourceID
			}
		}
	}

	return resourceID, nil
}

// GetEndpointResourceID 获取终端的资源ID（实现原项目EndpointResourceID逻辑）
func (r *adminUserRepo) GetEndpointResourceID(c *gin.Context, endpointID uint) (uint, error) {
	type endpointResourceResult struct {
		ResourceID uint `json:"resource_id"`
	}

	var result endpointResourceResult
	err := r.db.WithContext(c).
		Table("endpoint e").
		Select("IF(e.resource_id = 0, a.resource_id, e.resource_id) AS resource_id").
		Joins("LEFT JOIN agency a ON a.id = e.top_agency").
		Where("e.id = ?", endpointID).
		Take(&result).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return 0, nil
		}
		return 0, err
	}

	return result.ResourceID, nil
}

// ContainRoles 检查用户是否有指定角色中的任一个
func (r *adminUserRepo) ContainRoles(c *gin.Context, uid uint, roleNames ...string) (bool, error) {
	if len(roleNames) == 0 {
		return false, nil
	}

	// 获取用户角色
	roles, err := r.CheckRole(c, int(uid), -1) // -1表示获取所有类型的角色
	if err != nil {
		return false, err
	}

	// 检查是否包含指定角色
	for _, role := range roles {
		for _, targetRole := range roleNames {
			if role.Slug == targetRole {
				return true, nil
			}
		}
	}

	return false, nil
}

// GetUserAgency 获取用户代理信息（根据角色决定获取TopAgency还是SecondAgency）
func (r *adminUserRepo) GetUserAgency(c *gin.Context, uid uint) (*model.Agency, error) {
	// 首先检查用户是否有topAgency角色
	hasTopAgencyRole, err := r.ContainRoles(c, uid, "topAgency")
	if err != nil {
		return nil, err
	}

	// 第一步：从user_agency表获取agency_id
	var userAgency model.UserAgency
	err = r.db.WithContext(c).
		Where("uid = ?", uid).
		First(&userAgency).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	// 第二步：根据角色决定使用哪个agency_id
	var agencyID uint
	if hasTopAgencyRole && userAgency.TopAgency != nil {
		agencyID = uint(*userAgency.TopAgency)
	} else if userAgency.SecondAgency > 0 {
		agencyID = uint(userAgency.SecondAgency)
	} else {
		return nil, nil // 没有关联的agency
	}

	// 第三步：从agency表获取agency信息（这里会正确应用软删除）
	var agency model.Agency
	err = r.db.WithContext(c).
		Where("id = ?", agencyID).
		First(&agency).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return &agency, nil
}

// GetAgencyResourceID 获取代理的ResourceID（支持父级代理的ResourceID）
func (r *adminUserRepo) GetAgencyResourceID(c *gin.Context, agencyID uint) (uint, error) {
	type agencyResourceResult struct {
		ResourceID uint `json:"resource_id"`
	}

	var result agencyResourceResult
	err := r.db.WithContext(c).
		Table("agency a").
		Select("IF(p.id IS NULL, a.resource_id, p.resource_id) AS resource_id").
		Joins("LEFT JOIN agency p ON a.pid = p.id").
		Where("a.id = ?", agencyID).
		Take(&result).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return 0, nil
		}
		return 0, err
	}

	return result.ResourceID, nil
}
