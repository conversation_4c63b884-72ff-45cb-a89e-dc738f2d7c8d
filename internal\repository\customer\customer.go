package customer

import (
	"marketing-app/internal/model"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type Customer interface {
	GetWarrantyInfo(c *gin.Context, number string) (*model.Warranty, error)
	GetDeviceLastUse(c *gin.Context, tableName, number, weekEnd string, buyDate *time.Time) (*model.DeviceLastUse, error)
	GetDistributionOfUsage(c *gin.Context, tableName, number, weekEnd string) (*model.DistributionOfUsage, error)
	GetDeviceUsageHabit(c *gin.Context, tableName, number, weekEnd string) (*model.DeviceUsageHabit, error)
	GetDeviceTop10Use(c *gin.Context, tableName, number, weekEnd string) (*model.DeviceTop10Use, error)
	GetTableName(c *gin.Context, baseTableName, weekEnd string) (string, error)
	GetClientProfile(c *gin.Context, deviceID string) (*model.ClientProfile, error)
	GetClientProfileBatch(c *gin.Context, deviceIDs []string) ([]model.ClientProfile, error)
	GetTutorUsageSummary(c *gin.Context, tableName, number, weekEnd string) (*model.TutorUsageSummary, error)
	GetTutorUsageDetail(c *gin.Context, tableName, number, weekEnd string) (*model.TutorUsageDetail, error)
	GetTutorsplanUsageSummary(c *gin.Context, tableName, number, weekEnd string) (*model.TutorsplanUsageSummary, error)
	GetTutorsplanUsageDetail(c *gin.Context, tableName, number, weekEnd string) (*model.TutorsplanUsageDetail, error)
	GetDTeacherReportWeekly(c *gin.Context, uid int, weekend time.Time) (*model.DTeacherReport, error)
	AddDTeacherReportWeekly(c *gin.Context, report *model.DTeacherReport) error
}

type customer struct {
	db       *gorm.DB
	yxStatDb *gorm.DB
}

func NewCustomer(db *gorm.DB, yxStatDb *gorm.DB) Customer {
	return &customer{
		db:       db,
		yxStatDb: yxStatDb,
	}
}

// GetWarrantyInfo 获取保修信息
func (c *customer) GetWarrantyInfo(ctx *gin.Context, number string) (*model.Warranty, error) {
	var warranty model.Warranty
	err := c.db.WithContext(ctx).
		Where("number = ? AND status IN (0, 1)", number).
		First(&warranty).Error
	if err != nil {
		return nil, err
	}
	return &warranty, nil
}

// GetDeviceLastUse 获取设备最后使用信息
func (c *customer) GetDeviceLastUse(ctx *gin.Context, tableName, number, weekEnd string, buyDate *time.Time) (*model.DeviceLastUse, error) {
	if tableName == "" {
		return nil, gorm.ErrRecordNotFound
	}

	var deviceLastUse model.DeviceLastUse
	query := c.yxStatDb.WithContext(ctx).Table(tableName).Where("device_id = ? AND weekend = ?", number, weekEnd)
	if buyDate != nil {
		buyDateStr := buyDate.Format("2006-01-02")
		query = query.Where("weekend >= ?", buyDateStr)
	}
	err := query.First(&deviceLastUse).Error
	if err != nil {
		return nil, err
	}
	return &deviceLastUse, nil
}

// GetDistributionOfUsage 获取使用分布信息
func (c *customer) GetDistributionOfUsage(ctx *gin.Context, tableName, number, weekEnd string) (*model.DistributionOfUsage, error) {
	if tableName == "" {
		return nil, gorm.ErrRecordNotFound
	}

	var distributionOfUsage model.DistributionOfUsage
	err := c.yxStatDb.WithContext(ctx).Table(tableName).
		Where("device_id = ? AND weekend = ?", number, weekEnd).
		First(&distributionOfUsage).Error
	if err != nil {
		return nil, err
	}
	return &distributionOfUsage, nil
}

// GetDeviceUsageHabit 获取设备使用习惯信息
func (c *customer) GetDeviceUsageHabit(ctx *gin.Context, tableName, number, weekEnd string) (*model.DeviceUsageHabit, error) {
	if tableName == "" {
		return nil, gorm.ErrRecordNotFound
	}

	var deviceUsageHabit model.DeviceUsageHabit
	err := c.yxStatDb.WithContext(ctx).Table(tableName).
		Where("device_id = ? AND weekend = ?", number, weekEnd).
		First(&deviceUsageHabit).Error
	if err != nil {
		return nil, err
	}
	return &deviceUsageHabit, nil
}

// GetDeviceTop10Use 获取设备top10使用应用信息
func (c *customer) GetDeviceTop10Use(ctx *gin.Context, tableName, number, weekEnd string) (*model.DeviceTop10Use, error) {
	if tableName == "" {
		return nil, gorm.ErrRecordNotFound
	}

	var deviceTop10Use model.DeviceTop10Use
	err := c.yxStatDb.WithContext(ctx).Table(tableName).
		Where("device_id = ? AND weekend = ?", number, weekEnd).
		First(&deviceTop10Use).Error
	if err != nil {
		return nil, err
	}
	return &deviceTop10Use, nil
}

// GetTableName 获取表名，支持动态表名
func (c *customer) GetTableName(ctx *gin.Context, baseTableName, weekend string) (string, error) {
	now := time.Now()

	// 数据延后一天，周一没有本周的使用记录
	if now.Format("20060102") == weekend {
		return "", nil
	}

	// 计算本周周一
	weekday := now.Weekday()
	if weekday == time.Sunday {
		weekday = 7 // 将周日调整为7
	}
	thisMonday := now.AddDate(0, 0, -int(weekday-1))
	thisMondayStr := thisMonday.Format("20060102")

	if thisMondayStr == weekend {
		return baseTableName, nil
	}

	// 检查动态表是否存在
	tableName := baseTableName + "_" + weekend

	// 使用INFORMATION_SCHEMA查询表是否存在，支持参数绑定
	var count int64
	err := c.yxStatDb.WithContext(ctx).
		Table("INFORMATION_SCHEMA.TABLES").
		Where("TABLE_SCHEMA = DATABASE() AND TABLE_NAME = ?", tableName).
		Count(&count).Error
	if err != nil {
		return "", err
	}
	if count > 0 {
		return tableName, nil
	}

	return "", nil
}

// GetClientProfile 获取设备用户画像信息
func (c *customer) GetClientProfile(ctx *gin.Context, deviceID string) (*model.ClientProfile, error) {
	var clientProfile model.ClientProfile
	err := c.yxStatDb.WithContext(ctx).
		Where("device_id = ?", deviceID).
		First(&clientProfile).Error
	if err != nil {
		return nil, err
	}
	return &clientProfile, nil
}

// GetClientProfileBatch 批量获取设备用户画像信息
func (c *customer) GetClientProfileBatch(ctx *gin.Context, deviceIDs []string) ([]model.ClientProfile, error) {
	if len(deviceIDs) == 0 {
		return nil, nil
	}

	var clientProfiles []model.ClientProfile
	err := c.yxStatDb.WithContext(ctx).
		Where("device_id IN ?", deviceIDs).
		Find(&clientProfiles).Error
	if err != nil {
		return nil, err
	}
	return clientProfiles, nil
}

// GetTutorUsageSummary 获取教材全解使用总览
func (c *customer) GetTutorUsageSummary(ctx *gin.Context, tableName, number, weekEnd string) (*model.TutorUsageSummary, error) {
	if tableName == "" {
		return nil, gorm.ErrRecordNotFound
	}

	var tutorUsageSummary model.TutorUsageSummary
	err := c.yxStatDb.WithContext(ctx).Table(tableName).
		Where("device_id = ? AND weekend = ?", number, weekEnd).
		First(&tutorUsageSummary).Error
	if err != nil {
		return nil, err
	}
	return &tutorUsageSummary, nil
}

// GetTutorUsageDetail 获取教材全解使用详情
func (c *customer) GetTutorUsageDetail(ctx *gin.Context, tableName, number, weekEnd string) (*model.TutorUsageDetail, error) {
	if tableName == "" {
		return nil, gorm.ErrRecordNotFound
	}

	var tutorUsageDetail model.TutorUsageDetail
	err := c.yxStatDb.WithContext(ctx).Table(tableName).
		Where("device_id = ? AND weekend = ?", number, weekEnd).
		First(&tutorUsageDetail).Error
	if err != nil {
		return nil, err
	}
	return &tutorUsageDetail, nil
}

// GetTutorsplanUsageSummary 获取名师辅导班使用总览
func (c *customer) GetTutorsplanUsageSummary(ctx *gin.Context, tableName, number, weekEnd string) (*model.TutorsplanUsageSummary, error) {
	if tableName == "" {
		return nil, gorm.ErrRecordNotFound
	}

	var tutorsplanUsageSummary model.TutorsplanUsageSummary
	err := c.yxStatDb.WithContext(ctx).Table(tableName).
		Where("device_id = ? AND weekend = ?", number, weekEnd).
		First(&tutorsplanUsageSummary).Error
	if err != nil {
		return nil, err
	}
	return &tutorsplanUsageSummary, nil
}

// GetTutorsplanUsageDetail 获取名师辅导班使用详情
func (c *customer) GetTutorsplanUsageDetail(ctx *gin.Context, tableName, number, weekEnd string) (*model.TutorsplanUsageDetail, error) {
	if tableName == "" {
		return nil, gorm.ErrRecordNotFound
	}

	var tutorsplanUsageDetail model.TutorsplanUsageDetail
	err := c.yxStatDb.WithContext(ctx).Table(tableName).
		Where("device_id = ? AND weekend = ?", number, weekEnd).
		First(&tutorsplanUsageDetail).Error
	if err != nil {
		return nil, err
	}
	return &tutorsplanUsageDetail, nil
}

// GetDTeacherReportWeekly 获取双师周报数据
func (c *customer) GetDTeacherReportWeekly(ctx *gin.Context, uid int, weekend time.Time) (*model.DTeacherReport, error) {
	var report model.DTeacherReport
	err := c.yxStatDb.WithContext(ctx).
		Where("uid = ? AND weekend = ?", uid, weekend).
		First(&report).Error
	if err != nil {
		return nil, err
	}
	return &report, nil
}

// AddDTeacherReportWeekly 添加双师周报数据
func (c *customer) AddDTeacherReportWeekly(ctx *gin.Context, report *model.DTeacherReport) error {
	err := c.yxStatDb.WithContext(ctx).
		Where("uid = ? AND weekend = ?", report.UID, report.Weekend).
		Assign(model.DTeacherReport{
			Comment:                report.Comment,
			LessonList:             report.LessonList,
			ParticipatedNum:        report.ParticipatedNum,
			ParticipatedTime:       report.ParticipatedTime,
			SubjectsLessonsNumInfo: report.SubjectsLessonsNumInfo,
		}).
		FirstOrCreate(&report).Error
	return err
}
