package dto

import "time"

type MarketingCategoryOption struct {
	ID       uint                      `json:"id"`
	Name     string                    `json:"name"`
	ParentID uint                      `json:"parent_id"`
	Children []MarketingCategoryOption `json:"children,omitempty"`
}

type ArticleTag struct {
	ID   uint   `json:"id"`
	Name string `json:"name"`
}

type ArticleAttachmentResponse struct {
	Files        []string `json:"files"`
	Covers       []string `json:"covers"`
	Title        string   `json:"title"`
	Desc         string   `json:"desc"`
	Link         string   `json:"link"`
	Downloadable uint8    `json:"downloadable"`
}

type ArticleCommentCreatorInfo struct {
	ID          uint   `json:"id"`
	Name        string `json:"name"`
	Avatar      string `json:"avatar"`
	NumComments int    `json:"num_comments"`
}

// ==================== 新增终端用户接口相关DTO ====================

// 分类相关DTO
type CategoryListQuery struct {
	ParentID        uint
	CustomerVisible *uint8
	ResourceID      uint
}

type CategoryListItem struct {
	ID          uint   `json:"id"`
	Name        string `json:"name"`
	NumArticles uint   `json:"num_articles"`
}

// 活动相关DTO
type ActivityListItem struct {
	ID    uint   `json:"id"`
	Title string `json:"title"`
	Type  string `json:"type"`
}

// 销售统计DTO
type SalesStatistics struct {
	Sales      string `json:"sales"`
	SalesPrev1 string `json:"sales_prev_1"`
	SalesPrev2 string `json:"sales_prev_2"`
}

// 用户资料DTO
type UserProfile struct {
	Blocked uint8 `json:"blocked"`
}

// 终端文章列表项DTO
type ArticleDefaultListItem struct {
	ID             uint                       `json:"id"`
	Title          string                     `json:"title"`
	Content        string                     `json:"content"`
	Sleight        string                     `json:"sleight"`
	AttachmentType uint8                      `json:"attachment_type"`
	Attachment     *ArticleAttachmentResponse `json:"attachment,omitempty"`
	CategoryName   string                     `json:"category_name"`
	Tags           []ArticleTag               `json:"tags"`
	NumLikes       uint                       `json:"num_likes"`
	NumViews       uint                       `json:"num_views"`
	NumDownloads   uint                       `json:"num_downloads"`
	NumComments    uint                       `json:"num_comments"`
	Shareable      uint8                      `json:"shareable"`
	Creator        string                     `json:"creator"`
	PublisherName  string                     `json:"publisher_name"`
	IsLiked        bool                       `json:"is_liked"`
	IsViewed       bool                       `json:"is_viewed"`
	CreatedAt      time.Time                  `json:"created_at"`
}

// 文章列表查询参数DTO
type ArticleListQuery struct {
	UserID          uint
	ResourceID      uint
	CategoryID      uint
	TagID           uint
	CustomerVisible *uint8
	Keyword         string
	Page            int
	PageSize        int
}

// 文章详情DTO
type ArticleDetailItem struct {
	ID              uint                       `json:"id"`
	Title           string                     `json:"title"`
	Content         string                     `json:"content"`
	Sleight         string                     `json:"sleight"`
	AttachmentType  uint8                      `json:"attachment_type"`
	Attachment      *ArticleAttachmentResponse `json:"attachment,omitempty"`
	Tags            []ArticleTag               `json:"tags"`
	NumLikes        uint                       `json:"num_likes"`
	NumShares       uint                       `json:"num_shares"`
	NumViews        uint                       `json:"num_views"`
	NumComments     uint                       `json:"num_comments"`
	Shareable       uint8                      `json:"shareable"`
	WeWorkShareable uint8                      `json:"wework_shareable"`
	CommentSetting  uint8                      `json:"comment_setting"`
	Creator         string                     `json:"creator"`
	CreatorAvatar   string                     `json:"creator_avatar"`
	IsLiked         bool                       `json:"is_liked"`
	CreatedAt       time.Time                  `json:"created_at"`
	UpdatedAt       time.Time                  `json:"updated_at"`
}

// 文章列表项DTO
type ArticleListItem struct {
	ID              uint                       `json:"id"`
	Title           string                     `json:"title"`
	Content         string                     `json:"content"`
	Sleight         string                     `json:"sleight"`
	AttachmentType  uint8                      `json:"attachment_type"`
	Attachment      *ArticleAttachmentResponse `json:"attachment,omitempty"`
	Tags            []ArticleTag               `json:"tags"`
	NumShares       uint                       `json:"num_shares"`
	NumComments     uint                       `json:"num_comments"`
	NumViews        uint                       `json:"num_views"`
	NumLikes        uint                       `json:"num_likes"`
	IsLiked         bool                       `json:"is_liked"`
	Creator         string                     `json:"creator"`
	CreatorAvatar   string                     `json:"creator_avatar"`
	Shareable       uint8                      `json:"shareable"`
	WeWorkShareable uint8                      `json:"wework_shareable"`
	CreatedAt       time.Time                  `json:"created_at"`
}

// 终端评论查询DTO
type ArticleCommentListQuery struct {
	ArticleID uint
	UserID    uint
	Page      int
	PageSize  int
	ReplySize uint
	OrderBy   string
	Order     string
}

// 终端评论项DTO
type ArticleCommentItem struct {
	ID         uint                       `json:"id"`
	Content    string                     `json:"content"`
	Attachment interface{}                `json:"attachment,omitempty"`
	NumLikes   uint                       `json:"num_likes"`
	IsLiked    bool                       `json:"is_liked"`
	Creator    *ArticleCommentCreatorInfo `json:"creator"`
	CreatedAt  time.Time                  `json:"created_at"`
	Top        uint8                      `json:"top,omitempty"`
	Discarded  uint8                      `json:"discarded,omitempty"`
	Replies    *ArticleCommentReplies     `json:"replies,omitempty"`
}

// 终端评论回复列表DTO
type ArticleCommentReplies struct {
	List  []*ArticleCommentItem `json:"list"`
	Total uint                  `json:"total"`
}

// 文章搜索DTO
type ArticleSearchItem struct {
	ID    uint   `json:"id"`
	Title string `json:"title"`
}
