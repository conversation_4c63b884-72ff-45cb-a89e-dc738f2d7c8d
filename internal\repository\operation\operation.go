package operation

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"gorm.io/gorm"
	"marketing-app/internal/consts"
	"marketing-app/internal/model"
	"marketing-app/internal/pkg/oss"
	"marketing-app/internal/repository/operation/dto"
)

type Operation interface {
	GetActivities(c *gin.Context, endpointID uint, date string) ([]dto.ActivityListItem, error)
	GetSalesStatistics(c *gin.Context, endpointID uint, date string) (*dto.SalesStatistics, error)
	GetUserProfile(c *gin.Context, userID uint) (*dto.UserProfile, error)
	GetArticles(c *gin.Context, query *dto.ArticleListQuery) ([]*dto.ArticleListItem, error)
	GetCategories(c *gin.Context, query *dto.CategoryListQuery) ([]*dto.CategoryListItem, error)
	GetArticleDetail(c *gin.Context, articleID uint, userID uint, resourceID uint) (*dto.ArticleDetailItem, error)
	SearchArticles(c *gin.Context, keyword string) ([]*dto.ArticleSearchItem, error)
	LikeArticle(c *gin.Context, userID uint, articleID uint, like uint8) error
	ViewArticles(c *gin.Context, userID uint, articleIDs []uint) error
	DownloadArticle(c *gin.Context, userID uint, articleID uint) error
	GetArticleComments(c *gin.Context, query *dto.ArticleCommentListQuery) ([]*dto.ArticleCommentItem, error)
}

type operation struct {
	db         *gorm.DB
	ossService oss.OSSService
}

func NewOperation(db *gorm.DB, ossService oss.OSSService) Operation {
	return &operation{
		db:         db,
		ossService: ossService,
	}
}

// GetActivities 获取活动列表
func (o *operation) GetActivities(c *gin.Context, endpointID uint, date string) ([]dto.ActivityListItem, error) {
	var results []dto.ActivityListItem

	err := o.db.WithContext(c).
		Table("activity a").
		Select("a.id, a.title, t.name as type").
		Joins("INNER JOIN activity_type t ON a.type_id = t.id").
		Where("a.endpoint_id = ?", endpointID).
		Where("a.enabled = ?", 1).
		Where("a.start_time <= ?", date).
		Where("a.end_time >= ?", date).
		Where("a.deleted_at IS NULL").
		Scan(&results).Error

	if err != nil {
		return nil, errors.Wrap(err, "获取活动列表失败")
	}

	return results, nil
}

// GetSalesStatistics 获取销售统计
func (o *operation) GetSalesStatistics(c *gin.Context, endpointID uint, date string) (*dto.SalesStatistics, error) {
	// 解析日期并计算时间范围
	targetTime, err := time.Parse("2006-01-02", date)
	if err != nil {
		return nil, errors.Wrap(err, "日期格式错误")
	}

	// 计算前2个月的开始时间和当月的结束时间
	startTime := targetTime.AddDate(0, -2, 0)
	startTime = time.Date(startTime.Year(), startTime.Month(), 1, 0, 0, 0, 0, startTime.Location())
	endTime := time.Date(targetTime.Year(), targetTime.Month()+1, 1, 0, 0, 0, 0, targetTime.Location()).AddDate(0, 0, -1)

	// 定义月份映射
	keyDateMap := make(map[string]string)
	keyDateMap[startTime.Format("2006-01")] = "sales_prev_2"
	keyDateMap[startTime.AddDate(0, 1, 0).Format("2006-01")] = "sales_prev_1"
	keyDateMap[targetTime.Format("2006-01")] = "sales"

	// 查询销售数据
	type salesResult struct {
		Date  string `json:"date"`
		Sales string `json:"sales"`
	}

	var results []salesResult
	err = o.db.WithContext(c).
		Table("warranty").
		Select("DATE_FORMAT(buy_date, '%Y-%m') as date, SUM(customer_price) as sales").
		Where("endpoint = ?", endpointID).
		Where("status = ?", 1).
		Where("channel != ?", "agency_other").
		Where("buy_date BETWEEN ? AND ?", startTime, endTime).
		Group("DATE_FORMAT(buy_date, '%Y-%m')").
		Scan(&results).Error

	if err != nil {
		return nil, errors.Wrap(err, "获取销售统计失败")
	}

	// 初始化统计结果
	statistics := &dto.SalesStatistics{
		Sales:      "0",
		SalesPrev1: "0",
		SalesPrev2: "0",
	}

	// 设置查询结果
	for _, result := range results {
		switch keyDateMap[result.Date] {
		case "sales":
			statistics.Sales = result.Sales
		case "sales_prev_1":
			statistics.SalesPrev1 = result.Sales
		case "sales_prev_2":
			statistics.SalesPrev2 = result.Sales
		}
	}

	return statistics, nil
}

// GetUserProfile 获取用户资料
func (o *operation) GetUserProfile(c *gin.Context, userID uint) (*dto.UserProfile, error) {
	var profile dto.UserProfile

	err := o.db.WithContext(c).
		Table("op_user").
		Select("blocked").
		Where("user_id = ?", userID).
		Scan(&profile).Error

	if err != nil {
		// 如果记录不存在，返回默认值
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &dto.UserProfile{Blocked: 0}, nil
		}
		return nil, errors.Wrap(err, "获取用户资料失败")
	}

	return &profile, nil
}

// GetArticles 获取文章列表
func (o *operation) GetArticles(c *gin.Context, query *dto.ArticleListQuery) ([]*dto.ArticleListItem, error) {
	// 定义临时结构体来接收数据库查询结果
	type articleQueryResult struct {
		ID              uint      `json:"id"`
		Title           string    `json:"title"`
		Content         string    `json:"content"`
		Sleight         string    `json:"sleight"`
		AttachmentType  uint8     `json:"attachment_type"`
		Attachment      string    `json:"attachment"` // JSON字符串
		Shareable       uint8     `json:"shareable"`
		WeWorkShareable uint8     `json:"wework_shareable"`
		CreatedAt       time.Time `json:"created_at"`
		Creator         string    `json:"creator"`
		CreatorAvatar   string    `json:"creator_avatar"`
		IsLiked         bool      `json:"is_liked"`
	}

	var queryResults []articleQueryResult

	// 构建基础查询
	db := o.db.WithContext(c).
		Table("op_article a").
		Select(`a.id, a.title, a.content, a.sleight, a.attachment_type, a.attachment, 
			a.shareable, a.wework_shareable, a.created_at,
			COALESCE(p.name, u.name) as creator,
			COALESCE(p.avatar, u.avatar) as creator_avatar,
			CASE WHEN l.id IS NOT NULL THEN true ELSE false END as is_liked`).
		Joins("LEFT JOIN admin_users u ON a.created_by = u.id").
		Joins("LEFT JOIN op_publisher p ON a.publisher_id = p.id").
		Joins("LEFT JOIN op_article_category c ON a.category_id = c.id").
		Where("a.enabled = ?", 1).
		Where("a.category_enabled = ?", 1).
		Where("a.deleted_at IS NULL").
		Order("a.created_at DESC")

	// 添加点赞状态查询
	if query.UserID > 0 {
		db = db.Joins("LEFT JOIN op_like l ON a.id = l.target_id AND l.target = ? AND l.user_id = ?", consts.LikeTargetArticle, query.UserID)
	}

	// 资源ID逻辑：爱学空间(resourceId=30)只看自己的，其他不能看爱学空间
	if query.ResourceID == 30 {
		db = db.Where("c.resource_id = ?", query.ResourceID)
	} else {
		db = db.Where("c.resource_id != ?", 30)
	}

	// 添加筛选条件
	if query.CategoryID > 0 {
		// 获取分类及其子分类的所有ID
		categoryIDs, err := o.getDescendantAndSelfIds(c, query.CategoryID)
		if err != nil {
			return nil, errors.Wrap(err, "获取分类树失败")
		}
		if len(categoryIDs) > 0 {
			db = db.Where("a.category_id IN ?", categoryIDs)
		}
	}

	if query.TagID > 0 {
		db = db.Joins("INNER JOIN op_article_tag_relation atr ON a.id = atr.article_id").
			Where("atr.tag_id = ?", query.TagID)
	}

	if query.CustomerVisible != nil {
		db = db.Where("a.shareable = ?", *query.CustomerVisible)
	}

	if query.Keyword != "" {
		search := "%" + query.Keyword + "%"
		db = db.Where("a.title LIKE ? OR a.sleight LIKE ? OR a.content LIKE ?", search, search, search)
	}

	// 分页
	if query.Page > 0 && query.PageSize > 0 {
		offset := (query.Page - 1) * query.PageSize
		db = db.Offset(offset).Limit(query.PageSize)
	}

	// 执行查询
	if err := db.Scan(&queryResults).Error; err != nil {
		return nil, errors.Wrap(err, "查询文章列表失败")
	}

	if len(queryResults) == 0 {
		return []*dto.ArticleListItem{}, nil
	}

	// 转换查询结果，解析attachment JSON
	articles := make([]*dto.ArticleListItem, len(queryResults))
	for i, result := range queryResults {
		var attachment *dto.ArticleAttachmentResponse
		if result.AttachmentType > 0 && result.Attachment != "" {
			attachment = &dto.ArticleAttachmentResponse{}
			if err := json.Unmarshal([]byte(result.Attachment), attachment); err != nil {
				attachment = nil
			}
		}

		articles[i] = &dto.ArticleListItem{
			ID:              result.ID,
			Title:           result.Title,
			Content:         result.Content,
			Sleight:         result.Sleight,
			AttachmentType:  result.AttachmentType,
			Attachment:      attachment,
			Tags:            []dto.ArticleTag{}, // 稍后填充
			NumShares:       0,                  // 稍后填充
			NumComments:     0,                  // 稍后填充
			NumViews:        0,                  // 稍后填充
			NumLikes:        0,                  // 稍后填充
			IsLiked:         result.IsLiked,
			Creator:         result.Creator,
			CreatorAvatar:   result.CreatorAvatar,
			Shareable:       result.Shareable,
			WeWorkShareable: result.WeWorkShareable,
			CreatedAt:       result.CreatedAt,
		}
	}

	// 获取文章ID列表
	articleIDs := make([]uint, len(articles))
	for i, article := range articles {
		articleIDs[i] = article.ID
	}

	// 获取标签信息
	if err := o.fillArticleTags(c, articles, articleIDs); err != nil {
		return nil, err
	}

	// 获取统计数据
	if err := o.fillArticleStats(c, articles, articleIDs, query.UserID); err != nil {
		return nil, err
	}

	return articles, nil
}

// fillArticleTags 填充文章标签信息
func (o *operation) fillArticleTags(c *gin.Context, articles []*dto.ArticleListItem, articleIDs []uint) error {
	type articleTagResult struct {
		ArticleID uint   `json:"article_id"`
		TagID     uint   `json:"tag_id"`
		TagName   string `json:"tag_name"`
	}

	var tagResults []articleTagResult
	err := o.db.WithContext(c).
		Table("op_article_tag_relation r").
		Select("r.article_id, t.id as tag_id, t.name as tag_name").
		Joins("INNER JOIN op_article_tag t ON r.tag_id = t.id").
		Where("r.article_id IN ?", articleIDs).
		Order("r.article_id, t.id").
		Scan(&tagResults).Error
	if err != nil {
		return errors.Wrap(err, "获取文章标签失败")
	}

	// 按文章ID组织标签数据
	tagMap := make(map[uint][]dto.ArticleTag)
	for _, result := range tagResults {
		tagMap[result.ArticleID] = append(tagMap[result.ArticleID], dto.ArticleTag{
			ID:   result.TagID,
			Name: result.TagName,
		})
	}

	// 填充到文章列表
	for _, article := range articles {
		if tags, exists := tagMap[article.ID]; exists {
			article.Tags = tags
		} else {
			article.Tags = []dto.ArticleTag{}
		}
	}

	return nil
}

// fillArticleStats 填充文章统计信息
func (o *operation) fillArticleStats(c *gin.Context, articles []*dto.ArticleListItem, articleIDs []uint, userID uint) error {
	// 获取点赞数
	likeMap, err := o.getArticleLikeCounts(c, articleIDs)
	if err != nil {
		return err
	}

	// 获取浏览数
	viewMap, err := o.getArticleViewCounts(c, articleIDs)
	if err != nil {
		return err
	}

	// 获取分享数
	shareMap, err := o.getArticleShareCounts(c, articleIDs)
	if err != nil {
		return err
	}

	// 获取评论数
	commentMap, err := o.getArticleCommentCounts(c, articleIDs, userID)
	if err != nil {
		return err
	}

	// 填充统计数据
	for _, article := range articles {
		article.NumLikes = likeMap[article.ID]
		article.NumViews = viewMap[article.ID]
		article.NumShares = shareMap[article.ID]
		article.NumComments = commentMap[article.ID]
	}

	return nil
}

// getArticleLikeCounts 获取文章点赞数
func (o *operation) getArticleLikeCounts(c *gin.Context, articleIDs []uint) (map[uint]uint, error) {
	type countResult struct {
		TargetID uint `json:"target_id"`
		Count    uint `json:"count"`
	}

	var results []countResult
	err := o.db.WithContext(c).
		Table("op_like").
		Select("target_id, COUNT(*) as count").
		Where("target_id IN ? AND target = ?", articleIDs, consts.LikeTargetArticle).
		Group("target_id").
		Scan(&results).Error
	if err != nil {
		return nil, errors.Wrap(err, "获取点赞数失败")
	}

	countMap := make(map[uint]uint)
	for _, result := range results {
		countMap[result.TargetID] = result.Count
	}
	return countMap, nil
}

// getArticleViewCounts 获取文章浏览数
func (o *operation) getArticleViewCounts(c *gin.Context, articleIDs []uint) (map[uint]uint, error) {
	type countResult struct {
		TargetID uint `json:"target_id"`
		Count    uint `json:"count"`
	}

	var results []countResult
	err := o.db.WithContext(c).
		Table("op_view").
		Select("target_id, COUNT(*) as count").
		Where("target_id IN ? AND target = ?", articleIDs, consts.ViewTargetArticle).
		Group("target_id").
		Scan(&results).Error
	if err != nil {
		return nil, errors.Wrap(err, "获取浏览数失败")
	}

	countMap := make(map[uint]uint)
	for _, result := range results {
		countMap[result.TargetID] = result.Count
	}
	return countMap, nil
}

// getArticleShareCounts 获取文章分享数
func (o *operation) getArticleShareCounts(c *gin.Context, articleIDs []uint) (map[uint]uint, error) {
	type countResult struct {
		ObjectID uint `json:"object_id"`
		Count    uint `json:"count"`
	}

	var results []countResult
	err := o.db.WithContext(c).
		Table("share_info s").
		Select("s.object_id, COUNT(*) as count").
		Joins("INNER JOIN share_log l ON s.id = l.share_id").
		Where("s.object_id IN ? AND s.object = ?", articleIDs, consts.ArticleShareSlug).
		Group("s.object_id").
		Scan(&results).Error
	if err != nil {
		return nil, errors.Wrap(err, "获取分享数失败")
	}

	countMap := make(map[uint]uint)
	for _, result := range results {
		countMap[result.ObjectID] = result.Count
	}
	return countMap, nil
}

// getArticleCommentCounts 获取文章评论数（与原函数ArticleNumComments逻辑一致）
func (o *operation) getArticleCommentCounts(c *gin.Context, articleIDs []uint, userID uint) (map[uint]uint, error) {
	type countResult struct {
		ArticleID uint `json:"article_id"`
		Count     uint `json:"count"`
	}

	// 实现与原函数ArticleNumComments一致的复杂权限判断逻辑
	countSQL := fmt.Sprintf(`
		c.article_id,
		COUNT(
			CASE 
				WHEN c.created_by = %d THEN 1
				WHEN a.comment_setting = %d AND c.visible = 1 THEN 1
				WHEN a.comment_setting = %d AND c.selected = 1 THEN 1
			ELSE 
				null 
			END
		) as count`,
		userID,                                  // 自己创建的评论
		consts.ArticleCommentSettingNoNeedAudit, // 无需审核模式，visible=1的评论
		consts.ArticleCommentSettingNeedAudit,   // 需要审核模式，selected=1的评论
	)

	var results []countResult
	err := o.db.WithContext(c).
		Table("op_article_comment c").
		Select(countSQL).
		Joins("INNER JOIN op_article a ON c.article_id = a.id").
		Where("c.article_id IN ?", articleIDs).
		Group("c.article_id").
		Scan(&results).Error
	if err != nil {
		return nil, errors.Wrap(err, "获取评论数失败")
	}

	countMap := make(map[uint]uint)
	for _, result := range results {
		countMap[result.ArticleID] = result.Count
	}
	return countMap, nil
}

// getDescendantAndSelfIds 获取分类及其所有子分类的ID
func (o *operation) getDescendantAndSelfIds(c *gin.Context, categoryID uint) ([]uint, error) {
	var descendantIds []uint

	// 查找所有ancestor字段中包含当前分类ID的记录（即所有子分类）
	// 使用FIND_IN_SET函数在ancestor字段中查找分类ID
	err := o.db.WithContext(c).
		Table("op_article_category").
		Select("id").
		Where("FIND_IN_SET(?, ancestor)", categoryID).
		Scan(&descendantIds).Error

	if err != nil {
		return nil, err
	}

	// 将当前分类ID本身也加入到结果中
	descendantIds = append(descendantIds, categoryID)

	return descendantIds, nil
}

// GetCategories 获取分类列表
func (o *operation) GetCategories(c *gin.Context, query *dto.CategoryListQuery) ([]*dto.CategoryListItem, error) {
	var categories []*dto.CategoryListItem

	// 构建查询
	dbQuery := o.db.WithContext(c).
		Table("op_article_category").
		Select("id, name").
		Where("parent_id = ?", query.ParentID).
		Where("enabled = ?", 1).
		Order("`order` ASC, id ASC")

	// 根据ResourceID筛选
	if query.ResourceID == 30 {
		dbQuery = dbQuery.Where("resource_id = ?", query.ResourceID)
	} else {
		dbQuery = dbQuery.Where("resource_id != ?", 30)
	}

	err := dbQuery.Scan(&categories).Error
	if err != nil {
		return nil, errors.Wrap(err, "查询分类列表失败")
	}

	// 获取分类ID列表用于统计文章数量
	categoryIDs := make([]uint, len(categories))
	for i, category := range categories {
		categoryIDs[i] = category.ID
	}

	// 统计每个分类的文章数量（包括子分类）
	if len(categoryIDs) > 0 {
		articleCounts, err := o.countCategoryArticles(c, categoryIDs, query.CustomerVisible)
		if err != nil {
			return nil, err
		}

		// 设置文章数量
		for _, category := range categories {
			category.NumArticles = articleCounts[category.ID]
		}

		// 过滤掉文章数量为0的分类（原项目逻辑）
		filteredCategories := make([]*dto.CategoryListItem, 0)
		for _, category := range categories {
			if category.NumArticles > 0 {
				filteredCategories = append(filteredCategories, category)
			}
		}
		categories = filteredCategories
	}

	return categories, nil
}

// countCategoryArticles 统计分类文章数量（包括所有子分类的文章）
func (o *operation) countCategoryArticles(c *gin.Context, categoryIDs []uint, customerVisible *uint8) (map[uint]uint, error) {
	articleCounts := make(map[uint]uint)

	for _, categoryID := range categoryIDs {
		// 获取当前分类及其所有子分类的ID
		descendantIDs, err := o.getDescendantAndSelfIds(c, categoryID)
		if err != nil {
			return nil, errors.Wrap(err, "获取分类后代ID失败")
		}

		// 统计这些分类下的文章数量
		query := o.db.WithContext(c).
			Table("op_article").
			Where("category_id IN ?", descendantIDs).
			Where("enabled = ?", 1).
			Where("category_enabled = ?", 1) // 确保分类启用

		// 根据customerVisible参数筛选
		if customerVisible != nil {
			query = query.Where("shareable = ?", *customerVisible)
		}

		var count int64
		err = query.Count(&count).Error
		if err != nil {
			return nil, errors.Wrap(err, "统计分类文章数量失败")
		}

		articleCounts[categoryID] = uint(count)
	}

	return articleCounts, nil
}

// GetArticleDetail 获取文章详情
func (o *operation) GetArticleDetail(c *gin.Context, articleID uint, userID uint, resourceID uint) (*dto.ArticleDetailItem, error) {
	type queryResult struct {
		ID              uint      `json:"id"`
		Title           string    `json:"title"`
		Content         string    `json:"content"`
		Sleight         string    `json:"sleight"`
		AttachmentType  uint8     `json:"attachment_type"`
		Attachment      string    `json:"attachment"`
		Shareable       uint8     `json:"shareable"`
		WeWorkShareable uint8     `json:"wework_shareable"`
		CommentSetting  uint8     `json:"comment_setting"`
		Creator         string    `json:"creator"`
		CreatorAvatar   string    `json:"creator_avatar"`
		IsLiked         uint8     `json:"is_liked"`
		CreatedAt       time.Time `json:"created_at"`
		UpdatedAt       time.Time `json:"updated_at"`
	}

	var result queryResult

	// 构建主查询
	query := o.db.WithContext(c).
		Table("op_article a").
		Select(`a.id, a.title, a.content, a.sleight, a.attachment_type, a.attachment, 
				a.shareable, a.wework_shareable, a.comment_setting, a.created_at, a.updated_at,
				IF(p.id IS NOT NULL, p.name, u.name) AS creator, 
				IF(p.id IS NOT NULL, p.avatar, u.avatar) AS creator_avatar`).
		Joins("LEFT JOIN admin_users u ON a.created_by = u.id").
		Joins("LEFT JOIN op_publisher p ON a.publisher_id = p.id").
		Joins("LEFT JOIN op_article_category c ON a.category_id = c.id").
		Where("a.id = ?", articleID).
		Where("a.enabled = ?", 1).
		Where("a.category_enabled = ?", 1).
		Where("a.deleted_at IS NULL")

	// 如果有用户ID，添加点赞状态查询
	if userID > 0 {
		query = query.Joins("LEFT JOIN op_like l ON a.id = l.target_id AND l.target = ? AND l.user_id = ?", consts.LikeTargetArticle, userID).
			Select(`a.id, a.title, a.content, a.sleight, a.attachment_type, a.attachment, 
				a.shareable, a.wework_shareable, a.comment_setting, a.created_at, a.updated_at,
				IF(p.id IS NOT NULL, p.name, u.name) AS creator, 
				IF(p.id IS NOT NULL, p.avatar, u.avatar) AS creator_avatar,
				IF(l.id IS NOT NULL, 1, 0) AS is_liked`)
	} else {
		// 如果没有用户ID，is_liked字段设为0
		query = query.Select(`a.id, a.title, a.content, a.sleight, a.attachment_type, a.attachment, 
				a.shareable, a.wework_shareable, a.comment_setting, a.created_at, a.updated_at,
				IF(p.id IS NOT NULL, p.name, u.name) AS creator, 
				IF(p.id IS NOT NULL, p.avatar, u.avatar) AS creator_avatar,
				0 AS is_liked`)
	}

	// ResourceId权限筛选
	if resourceID == 30 {
		query = query.Where("c.resource_id = ?", resourceID)
	} else {
		query = query.Where("c.resource_id != ?", 30)
	}

	err := query.Take(&result).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil // 文章不存在
		}
		return nil, errors.Wrap(err, "查询文章详情失败")
	}

	// 解析附件JSON
	var attachment *dto.ArticleAttachmentResponse
	if result.AttachmentType > 0 && result.Attachment != "" {
		attachment = &dto.ArticleAttachmentResponse{}
		if err := json.Unmarshal([]byte(result.Attachment), attachment); err != nil {
			attachment = nil
		}
	}

	// 获取标签信息
	var tags []dto.ArticleTag
	err = o.db.WithContext(c).
		Table("op_article_tag_relation r").
		Select("t.id, t.name").
		Joins("INNER JOIN op_article_tag t ON r.tag_id = t.id").
		Where("r.article_id = ?", articleID).
		Order("r.tag_id ASC").
		Scan(&tags).Error
	if err != nil {
		return nil, errors.Wrap(err, "获取文章标签失败")
	}

	// 创建临时ArticleListItem来复用fillArticleStats函数
	tempArticle := &dto.ArticleListItem{
		ID:         result.ID,
		Tags:       tags,
		Attachment: attachment,
		IsLiked:    result.IsLiked == 1,
		// 其他字段稍后会被统计数据填充
	}
	tempArticles := []*dto.ArticleListItem{tempArticle}
	articleIDs := []uint{articleID}

	// 复用fillArticleStats获取所有统计数据
	if err := o.fillArticleStats(c, tempArticles, articleIDs, userID); err != nil {
		return nil, errors.Wrap(err, "获取统计数据失败")
	}

	// 组装结果（从fillArticleStats填充的临时对象中获取统计数据）
	article := &dto.ArticleDetailItem{
		ID:              result.ID,
		Title:           result.Title,
		Content:         result.Content,
		Sleight:         result.Sleight,
		AttachmentType:  result.AttachmentType,
		Attachment:      attachment,
		Tags:            tags,
		NumLikes:        tempArticle.NumLikes,
		NumShares:       tempArticle.NumShares,
		NumViews:        tempArticle.NumViews,
		NumComments:     tempArticle.NumComments,
		Shareable:       result.Shareable,
		WeWorkShareable: result.WeWorkShareable,
		CommentSetting:  result.CommentSetting,
		Creator:         result.Creator,
		CreatorAvatar:   result.CreatorAvatar,
		IsLiked:         result.IsLiked == 1,
		CreatedAt:       result.CreatedAt,
		UpdatedAt:       result.UpdatedAt,
	}

	return article, nil
}

// SearchArticles 搜索文章
func (o *operation) SearchArticles(c *gin.Context, keyword string) ([]*dto.ArticleSearchItem, error) {
	var articles []*dto.ArticleSearchItem

	// 构造模糊查询关键词
	search := "%" + keyword + "%"

	err := o.db.WithContext(c).
		Table("op_article").
		Select("id, title").
		Where("enabled = ?", 1).
		Where("category_enabled = ?", 1).
		Where("title LIKE ? OR sleight LIKE ? OR content LIKE ?", search, search, search).
		Scan(&articles).Error

	if err != nil {
		return nil, errors.Wrap(err, "搜索文章失败")
	}

	return articles, nil
}

// LikeArticle 点赞或取消点赞文章
func (o *operation) LikeArticle(c *gin.Context, userID uint, articleID uint, like uint8) error {
	if like == 1 {
		// 点赞：使用FirstOrCreate避免重复插入
		var likeRecord model.OpLike
		result := o.db.WithContext(c).
			Where(&model.OpLike{
				UserID:   userID,
				TargetID: articleID,
				Target:   consts.LikeTargetArticle,
			}).
			FirstOrCreate(&likeRecord)

		if result.Error != nil {
			return errors.Wrap(result.Error, "点赞文章失败")
		}
	} else {
		// 取消点赞：删除记录
		result := o.db.WithContext(c).
			Where("user_id = ? AND target_id = ? AND target = ?",
				userID, articleID, consts.LikeTargetArticle).
			Delete(&model.OpLike{})

		if result.Error != nil {
			return errors.Wrap(result.Error, "取消点赞失败")
		}
	}

	return nil
}

// ViewArticles 批量记录文章浏览
func (o *operation) ViewArticles(c *gin.Context, userID uint, articleIDs []uint) error {
	// 使用事务批量插入浏览记录
	return o.db.WithContext(c).Transaction(func(tx *gorm.DB) error {
		for _, articleID := range articleIDs {
			// 使用FirstOrCreate避免重复插入
			var viewRecord model.OpView
			result := tx.Where(&model.OpView{
				UserID:   userID,
				TargetID: articleID,
				Target:   consts.ViewTargetArticle,
			}).FirstOrCreate(&viewRecord)

			if result.Error != nil {
				return errors.Wrapf(result.Error, "记录文章%d浏览失败", articleID)
			}
		}
		return nil
	})
}

// DownloadArticle 记录文章下载并增加下载计数
func (o *operation) DownloadArticle(c *gin.Context, userID uint, articleID uint) error {
	// 使用事务执行两个操作：记录下载和增加计数
	return o.db.WithContext(c).Transaction(func(tx *gorm.DB) error {
		// 1. 插入下载记录
		downloadRecord := model.OpDownload{
			UserID:   userID,
			TargetID: articleID,
			Target:   consts.DownloadTargetArticle,
		}

		result := tx.Create(&downloadRecord)
		if result.Error != nil {
			return errors.Wrap(result.Error, "记录文章下载失败")
		}

		// 2. 增加文章下载计数
		result = tx.Model(&model.OpArticle{}).
			Where("id = ?", articleID).
			UpdateColumn("num_downloads", gorm.Expr("num_downloads + ?", 1))

		if result.Error != nil {
			return errors.Wrap(result.Error, "更新文章下载计数失败")
		}

		return nil
	})
}

// GetArticleComments 获取文章评论列表
func (o *operation) GetArticleComments(c *gin.Context, query *dto.ArticleCommentListQuery) ([]*dto.ArticleCommentItem, error) {
	// 1. 检查文章评论设置
	var commentSetting uint8
	result := o.db.WithContext(c).
		Model(&model.OpArticle{}).
		Where("id = ?", query.ArticleID).
		Select("comment_setting").
		Scan(&commentSetting)

	if result.Error != nil {
		return nil, errors.Wrap(result.Error, "获取文章评论设置失败")
	}

	if result.RowsAffected == 0 {
		return nil, errors.New("文章不存在")
	}

	// 2. 如果评论被禁止，返回空列表
	if commentSetting == consts.ArticleCommentSettingBanned {
		return []*dto.ArticleCommentItem{}, nil
	}

	// 3. 构建查询
	db := o.db.WithContext(c).
		Table("op_article_comment c").
		Select(`c.id, c.content, c.attachment, c.created_at, c.created_by, c.top, c.discarded,
				IF(ul.id IS NULL, 0, 1) as is_liked`).
		Joins("LEFT JOIN op_like ul ON ul.target_id = c.id AND ul.target = ? AND ul.user_id = ?",
			consts.LikeTargetComment, query.UserID).
		Where("c.article_id = ?", query.ArticleID).
		Where("c.source_id = 0") // 只获取顶级评论

	// 4. 根据评论设置过滤
	if commentSetting == consts.ArticleCommentSettingNeedAudit {
		db = db.Where("c.selected = 1 OR c.created_by = ?", query.UserID)
	} else if commentSetting == consts.ArticleCommentSettingNoNeedAudit {
		db = db.Where("c.visible = 1 OR c.created_by = ?", query.UserID)
	}

	// 5. 处理排序
	joinLike := false
	if query.OrderBy == "num_likes" {
		db = db.Joins("LEFT JOIN op_like l ON c.id = l.target_id AND l.target = ?", consts.LikeTargetComment).
			Select("c.id, c.content, c.attachment, c.created_at, c.created_by, c.top, c.discarded, IF(ul.id IS NULL, 0, 1) as is_liked, COUNT(IF(l.id IS NOT NULL, 1, NULL)) as num_likes").
			Group("c.id").
			Order(fmt.Sprintf("num_likes %s", query.Order))
		joinLike = true
	} else if query.OrderBy == "created_at" {
		db = db.Order(fmt.Sprintf("c.created_at %s", query.Order))
	} else {
		db = db.Order("c.top DESC, c.id DESC")
	}

	// 6. 分页
	if query.Page > 0 && query.PageSize > 0 {
		offset := (query.Page - 1) * query.PageSize
		db = db.Offset(offset).Limit(query.PageSize)
	}

	// 7. 执行查询获取评论
	var commentResults []struct {
		ID         uint      `json:"id"`
		Content    string    `json:"content"`
		Attachment string    `json:"attachment"`
		CreatedAt  time.Time `json:"created_at"`
		CreatedBy  uint      `json:"created_by"`
		Top        uint8     `json:"top"`
		Discarded  uint8     `json:"discarded"`
		IsLiked    bool      `json:"is_liked"`
		NumLikes   uint      `json:"num_likes"`
	}

	if err := db.Scan(&commentResults).Error; err != nil {
		return nil, errors.Wrap(err, "获取评论列表失败")
	}

	if len(commentResults) == 0 {
		return []*dto.ArticleCommentItem{}, nil
	}

	// 8. 收集评论ID和用户ID，用于后续查询
	commentIDs := make([]uint, len(commentResults))
	userIDs := make([]uint, len(commentResults))
	for i, comment := range commentResults {
		commentIDs[i] = comment.ID
		userIDs[i] = comment.CreatedBy
	}

	// 9. 获取评论的回复
	replies, err := o.getCommentReplies(c, commentIDs, query.UserID, query.ReplySize, commentSetting)
	if err != nil {
		return nil, errors.Wrap(err, "获取评论回复失败")
	}

	// 10. 收集所有用户ID（包括回复者）
	allUserIDs := append([]uint{}, userIDs...)
	for _, replyList := range replies {
		if replyList != nil {
			for _, reply := range replyList.List {
				if reply.Creator != nil {
					allUserIDs = append(allUserIDs, reply.Creator.ID)
				}
			}
		}
	}

	// 11. 获取点赞数量（如果不是通过JOIN获取）
	likeCounts := make(map[uint]uint)
	if !joinLike {
		allCommentIDs := append([]uint{}, commentIDs...)
		for _, replyList := range replies {
			if replyList != nil {
				for _, reply := range replyList.List {
					allCommentIDs = append(allCommentIDs, reply.ID)
				}
			}
		}

		likeCounts, err = o.getCommentLikeCounts(c, allCommentIDs)
		if err != nil {
			return nil, errors.Wrap(err, "获取点赞数量失败")
		}
	}

	// 12. 获取用户信息
	userInfos, err := o.getUserInfos(c, allUserIDs)
	if err != nil {
		return nil, errors.Wrap(err, "获取用户信息失败")
	}

	// 13. 构建响应数据
	result_comments := make([]*dto.ArticleCommentItem, len(commentResults))
	for i, comment := range commentResults {
		// 处理附件
		var attachment interface{}
		if comment.Attachment != "" {
			if err := json.Unmarshal([]byte(comment.Attachment), &attachment); err != nil {
				attachment = []string{} // 解析失败时使用空数组
			}
		} else {
			attachment = []string{}
		}

		// 处理内容（如果是丢弃状态，清空内容）
		content := comment.Content
		if comment.Discarded == 1 {
			content = ""
		}

		// 获取点赞数
		numLikes := comment.NumLikes
		if !joinLike {
			numLikes = likeCounts[comment.ID]
		}

		// 获取创建者信息
		var creator *dto.ArticleCommentCreatorInfo
		if userInfo, exists := userInfos[comment.CreatedBy]; exists {
			creator = userInfo
		}

		result_comments[i] = &dto.ArticleCommentItem{
			ID:         comment.ID,
			Content:    content,
			Attachment: attachment,
			NumLikes:   numLikes,
			IsLiked:    comment.IsLiked,
			Creator:    creator,
			CreatedAt:  comment.CreatedAt,
			Top:        comment.Top,
			Discarded:  comment.Discarded,
			Replies:    replies[comment.ID],
		}
	}

	return result_comments, nil
}

// getCommentReplies 获取评论回复
func (o *operation) getCommentReplies(c *gin.Context, commentIDs []uint, userID uint, replySize uint, commentSetting uint8) (map[uint]*dto.ArticleCommentReplies, error) {
	if len(commentIDs) == 0 {
		return make(map[uint]*dto.ArticleCommentReplies), nil
	}

	// 查询回复
	var replyResults []struct {
		ID         uint      `json:"id"`
		Content    string    `json:"content"`
		Attachment string    `json:"attachment"`
		CreatedAt  time.Time `json:"created_at"`
		CreatedBy  uint      `json:"created_by"`
		SourceID   uint      `json:"source_id"`
		Visible    uint8     `json:"visible"`
		Selected   uint8     `json:"selected"`
		Discarded  uint8     `json:"discarded"`
	}

	db := o.db.WithContext(c).
		Table("op_article_comment").
		Select("id, content, attachment, created_at, created_by, source_id, visible, selected, discarded").
		Where("source_id IN ?", commentIDs).
		Order("created_at ASC")

	// 根据评论设置过滤回复
	if commentSetting == consts.ArticleCommentSettingNeedAudit {
		db = db.Where("selected = 1 OR created_by = ?", userID)
	} else if commentSetting == consts.ArticleCommentSettingNoNeedAudit {
		db = db.Where("visible = 1 OR created_by = ?", userID)
	}

	if err := db.Scan(&replyResults).Error; err != nil {
		return nil, errors.Wrap(err, "获取回复失败")
	}

	// 收集回复ID和用户ID
	replyIDs := make([]uint, len(replyResults))
	replyUserIDs := make([]uint, len(replyResults))
	for i, reply := range replyResults {
		replyIDs[i] = reply.ID
		replyUserIDs[i] = reply.CreatedBy
	}

	// 获取回复的点赞状态
	replyLikedMap := make(map[uint]bool)
	if len(replyIDs) > 0 {
		var likedReplies []uint
		err := o.db.WithContext(c).
			Table("op_like").
			Select("target_id").
			Where("user_id = ? AND target = ? AND target_id IN ?", userID, consts.LikeTargetComment, replyIDs).
			Pluck("target_id", &likedReplies).Error

		if err != nil {
			return nil, errors.Wrap(err, "获取回复点赞状态失败")
		}

		for _, replyID := range likedReplies {
			replyLikedMap[replyID] = true
		}
	}

	// 获取回复的点赞数量
	replyLikeCounts, err := o.getCommentLikeCounts(c, replyIDs)
	if err != nil {
		return nil, errors.Wrap(err, "获取回复点赞数量失败")
	}

	// 获取回复者信息
	replyUserInfos, err := o.getUserInfos(c, replyUserIDs)
	if err != nil {
		return nil, errors.Wrap(err, "获取回复者信息失败")
	}

	// 组织回复数据
	repliesMap := make(map[uint][]*dto.ArticleCommentItem)
	for _, reply := range replyResults {
		// 处理附件
		var attachment interface{}
		if reply.Attachment != "" {
			if err := json.Unmarshal([]byte(reply.Attachment), &attachment); err != nil {
				attachment = []string{}
			}
		} else {
			attachment = []string{}
		}

		// 处理内容（如果是丢弃状态，清空内容）
		content := reply.Content
		if reply.Discarded == 1 {
			content = ""
		}

		// 获取创建者信息
		var creator *dto.ArticleCommentCreatorInfo
		if userInfo, exists := replyUserInfos[reply.CreatedBy]; exists {
			creator = userInfo
		}

		replyItem := &dto.ArticleCommentItem{
			ID:         reply.ID,
			Content:    content,
			Attachment: attachment,
			NumLikes:   replyLikeCounts[reply.ID],
			IsLiked:    replyLikedMap[reply.ID],
			Creator:    creator,
			CreatedAt:  reply.CreatedAt,
			Discarded:  reply.Discarded,
		}

		repliesMap[reply.SourceID] = append(repliesMap[reply.SourceID], replyItem)
	}

	// 按每个评论限制回复数量并构建结果
	result := make(map[uint]*dto.ArticleCommentReplies)
	for _, commentID := range commentIDs {
		replies := repliesMap[commentID]
		total := uint(len(replies))

		// 确保replies不为nil，避免JSON序列化问题
		if replies == nil {
			replies = []*dto.ArticleCommentItem{}
		}

		// 限制回复数量
		if replySize > 0 && len(replies) > int(replySize) {
			replies = replies[:replySize]
		}

		result[commentID] = &dto.ArticleCommentReplies{
			List:  replies,
			Total: total,
		}
	}

	return result, nil
}

// getCommentLikeCounts 获取评论点赞数量
func (o *operation) getCommentLikeCounts(c *gin.Context, commentIDs []uint) (map[uint]uint, error) {
	if len(commentIDs) == 0 {
		return make(map[uint]uint), nil
	}

	var results []struct {
		TargetID uint `json:"target_id"`
		Count    uint `json:"count"`
	}

	err := o.db.WithContext(c).
		Table("op_like").
		Select("target_id, COUNT(*) as count").
		Where("target = ? AND target_id IN ?", consts.LikeTargetComment, commentIDs).
		Group("target_id").
		Scan(&results).Error

	if err != nil {
		return nil, errors.Wrap(err, "获取点赞数量失败")
	}

	likeCounts := make(map[uint]uint)
	for _, result := range results {
		likeCounts[result.TargetID] = result.Count
	}

	return likeCounts, nil
}

// getUserInfos 获取用户信息
func (o *operation) getUserInfos(c *gin.Context, userIDs []uint) (map[uint]*dto.ArticleCommentCreatorInfo, error) {
	if len(userIDs) == 0 {
		return make(map[uint]*dto.ArticleCommentCreatorInfo), nil
	}

	// 去重用户ID
	uniqueUserIDs := make([]uint, 0, len(userIDs))
	userIDSet := make(map[uint]bool)
	for _, userID := range userIDs {
		if !userIDSet[userID] {
			uniqueUserIDs = append(uniqueUserIDs, userID)
			userIDSet[userID] = true
		}
	}

	var userResults []struct {
		ID     uint   `json:"id"`
		Name   string `json:"name"`
		Avatar string `json:"avatar"`
	}

	// 查询用户基本信息 TODO:需要还原准确的admin_user表查询
	err := o.db.WithContext(c).
		Table("admin_users").
		Select("id, name, avatar").
		Where("id IN ?", uniqueUserIDs).
		Scan(&userResults).Error

	if err != nil {
		return nil, errors.Wrap(err, "获取用户信息失败")
	}

	// 获取用户评论数量统计
	var commentCounts []struct {
		CreatedBy    uint `json:"created_by"`
		CommentCount int  `json:"comment_count"`
	}

	err = o.db.WithContext(c).
		Table("op_article_comment").
		Select("created_by, COUNT(*) as comment_count").
		Where("created_by IN ?", uniqueUserIDs).
		Group("created_by").
		Scan(&commentCounts).Error

	if err != nil {
		return nil, errors.Wrap(err, "获取用户评论数量失败")
	}

	// 构建用户评论数量映射
	userCommentCounts := make(map[uint]int)
	for _, count := range commentCounts {
		userCommentCounts[count.CreatedBy] = count.CommentCount
	}

	// 构建结果映射
	userInfos := make(map[uint]*dto.ArticleCommentCreatorInfo)
	for _, user := range userResults {
		userInfos[user.ID] = &dto.ArticleCommentCreatorInfo{
			ID:          user.ID,
			Name:        user.Name,
			Avatar:      user.Avatar,
			NumComments: userCommentCounts[user.ID],
		}
	}

	return userInfos, nil
}
