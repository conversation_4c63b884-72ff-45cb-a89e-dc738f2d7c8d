package repository

import (
	"marketing-app/internal/model"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type TabletUpdateRepository interface {
	// GetTabletUpdatesByIDs 根据ID列表获取平板更新记录
	GetTabletUpdatesByIDs(c *gin.Context, ids []uint) ([]model.AppNotificationTabletUpdate, error)
	// GetDistinctUpdateDates 获取指定类型的不同更新日期
	GetDistinctUpdateDates(c *gin.Context, updateType string, page, pageSize int) ([]string, error)
	// GetUpdatesByTypeAndDateRange 根据类型和日期范围获取更新记录
	GetUpdatesByTypeAndDateRange(c *gin.Context, updateType, startDate, endDate string) ([]model.AppNotificationTabletUpdate, error)
}

type tabletUpdateRepository struct {
	db *gorm.DB
}

func NewTabletUpdateRepository(db *gorm.DB) TabletUpdateRepository {
	return &tabletUpdateRepository{
		db: db,
	}
}

// GetTabletUpdatesByIDs 根据ID列表获取平板更新记录
func (r *tabletUpdateRepository) GetTabletUpdatesByIDs(c *gin.Context, ids []uint) ([]model.AppNotificationTabletUpdate, error) {
	var updates []model.AppNotificationTabletUpdate

	err := r.db.WithContext(c).
		Where("id IN ?", ids).
		Order("type").
		Order("id").
		Find(&updates).Error

	return updates, err
}

// GetDistinctUpdateDates 获取指定类型的不同更新日期
func (r *tabletUpdateRepository) GetDistinctUpdateDates(c *gin.Context, updateType string, page, pageSize int) ([]string, error) {
	var dates []string

	offset := (page - 1) * pageSize

	err := r.db.WithContext(c).
		Model(&model.AppNotificationTabletUpdate{}).
		Select("DISTINCT DATE_FORMAT(updated_at, '%Y-%m-%d') as updated_date").
		Where("type = ?", updateType).
		Order("updated_date DESC").
		Limit(pageSize).
		Offset(offset).
		Pluck("updated_date", &dates).Error

	return dates, err
}

// GetUpdatesByTypeAndDateRange 根据类型和日期范围获取更新记录
func (r *tabletUpdateRepository) GetUpdatesByTypeAndDateRange(c *gin.Context, updateType, startDate, endDate string) ([]model.AppNotificationTabletUpdate, error) {
	var updates []model.AppNotificationTabletUpdate

	err := r.db.WithContext(c).
		Where("type = ?", updateType).
		Where("updated_at BETWEEN ? AND ?", startDate, endDate).
		Order("updated_at DESC, id ASC").
		Select("id, data, updated_at").
		Find(&updates).Error

	return updates, err
}
