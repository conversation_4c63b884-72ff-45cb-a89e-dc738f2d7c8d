package customer

import (
	"marketing-app/internal/handler/customer"
	"marketing-app/internal/pkg/db"
	customerRepo "marketing-app/internal/repository/customer"
	customerSvc "marketing-app/internal/service/customer"

	"github.com/gin-gonic/gin"
)

type CustomerRouter interface {
	Register(r *gin.RouterGroup)
}

type customerRouter struct{}

func NewCustomerRouter() CustomerRouter {
	return &customerRouter{}
}

func (cr *customerRouter) Register(r *gin.RouterGroup) {
	// 获取数据库连接
	database := db.MustGetDB()
	yxStatDb := db.MustGetDB("yx_stat")

	// 初始化各层组件
	customerRepository := customerRepo.NewCustomer(database, yxStatDb)
	customerService := customerSvc.NewCustomerService(customerRepository)
	customerHandler := customer.NewCustomerHandler(customerService)

	// 注册路由
	customerGroup := r.Group("")
	{
		// 获取设备使用详情
		customerGroup.GET("/device_used_detail", customerHandler.GetDeviceUsedDetail)
		// 获取设备使用总览
		customerGroup.GET("/device_usage_summary", customerHandler.GetDeviceUsageSummary)
		// 批量获取设备使用总览
		customerGroup.GET("/device_usage_summary_batch", customerHandler.GetDeviceUsageSummaryBatch)
		// 获取教材全解使用总览
		customerGroup.GET("/tutor_usage_summary", customerHandler.GetTutorUsageSummary)
		// 获取教材全解使用详情
		customerGroup.GET("/tutor_usage_detail", customerHandler.GetTutorUsageDetail)
		// 获取名师辅导班使用总览
		customerGroup.GET("/tutors_plan_usage_summary", customerHandler.GetTutorsplanUsageSummary)
		// 获取名师辅导班使用详情
		customerGroup.GET("/tutors_plan_usage_detail", customerHandler.GetTutorsplanUsageDetail)
		// 获取双师数据
		customerGroup.GET("/dteacher_data", customerHandler.GetDTeacherData)
		// 获取双师周报数据
		customerGroup.GET("/dteacher_report_weekly", customerHandler.GetDTeacherReportWeekly)
		// 获取双师周数据列表
		customerGroup.GET("/dteacher_report_weeks", customerHandler.GetDTeacherReportWeeks)
	}
}
