package router

import (
	handler "marketing-app/internal/handler"
	"marketing-app/internal/pkg/db"
	"marketing-app/internal/repository"
	"marketing-app/internal/service"

	"github.com/gin-gonic/gin"
)

type NoticeRouter struct {
	notification handler.NotificationHandler
}

func NewNoticeRouter() *NoticeRouter {
	database, _ := db.GetDB()
	repo := repository.NewNotificationRepository(database)
	tabletUpdateRepo := repository.NewTabletUpdateRepository(database)
	svc := service.NewNotificationService(repo, tabletUpdateRepo)
	h := handler.NewNotificationHandler(svc)
	return &NoticeRouter{
		notification: h,
	}
}

func (p *NoticeRouter) Register(r *gin.RouterGroup) {
	g := r.Group("")
	{
		g.GET("/types", p.notification.NotificationType)
		g.GET("", p.notification.Notification)
		g.GET("/:id", p.notification.NotificationDetail)
		g.POST("/read", p.notification.Read)
		g.POST("/checked", p.notification.Checked)
	}
}
