package operation

import (
	"marketing-app/internal/handler/operation"
	"marketing-app/internal/pkg/db"
	"marketing-app/internal/pkg/oss"
	"marketing-app/internal/repository"
	operationRepo "marketing-app/internal/repository/operation"
	operationSvc "marketing-app/internal/service/operation"

	"github.com/gin-gonic/gin"
)

type OperationRouter interface {
	Register(r *gin.RouterGroup)
}

type operationRouter struct{}

func NewOperationRouter() OperationRouter {
	return &operationRouter{}
}

func (or *operationRouter) Register(r *gin.RouterGroup) {
	// 获取数据库连接
	database, _ := db.GetDB()

	// 初始化OSS服务
	ossService, _ := oss.NewOSSService()

	// 初始化各层组件
	operationRepository := operationRepo.NewOperation(database, ossService)
	adminUserRepository := repository.NewAdminUserRepository(database)
	operationService := operationSvc.NewOperationService(operationRepository, adminUserRepository)
	operationHandler := operation.NewOperationHandler(operationService)

	// App端接口
	r.GET("/activities", operationHandler.GetActivities)            // 获取推广活动数据
	r.GET("/sales/statistics", operationHandler.GetSalesStatistics) // 获取终端销售情况
	r.GET("/users/me/profile", operationHandler.GetUserProfile)     // 获取登录用户本人的基本信息

	// 终端文章接口
	articlesGroup := r.Group("/articles")
	{
		articlesGroup.GET("/", operationHandler.GetArticles)         // 获取文章列表
		articlesGroup.GET("/categories", operationHandler.GetCategories) // 获取文章分类选项
		articlesGroup.GET("/:id", operationHandler.GetArticleDetail) // 获取文章详情
		articlesGroup.GET("/search", operationHandler.SearchArticles) // 文章检索
		articlesGroup.POST("/:id/liked", operationHandler.LikeArticle) // 文章点赞
		articlesGroup.POST("/viewed", operationHandler.ViewArticles) // 文章浏览
		articlesGroup.POST("/:id/downloaded", operationHandler.DownloadArticle) // 文章下载
		
		// 终端评论接口
		articlesGroup.GET("/:id/comments", operationHandler.GetArticleComments) // 获取文章评论列表
		//articlesGroup.GET("/comments/:id/replies", operationHandler.GetFrontendCommentReplies) // 获取评论回复
	}
	//
	//// 终端评论管理接口
	//commentGroup := r.Group("/article-comments")
	//{
	//	commentGroup.POST("/", operationHandler.CreateFrontendComment)       // 创建评论
	//	commentGroup.POST("/:id/like", operationHandler.LikeFrontendComment) // 评论点赞
	//}

}
