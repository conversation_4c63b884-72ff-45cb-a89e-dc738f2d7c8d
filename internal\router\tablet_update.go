package router

import (
	"marketing-app/internal/handler"
	"marketing-app/internal/pkg/db"
	"marketing-app/internal/repository"
	"marketing-app/internal/service"

	"github.com/gin-gonic/gin"
)

type TabletUpdateRouter interface {
	Register(r *gin.RouterGroup)
}

type tabletUpdateRouter struct{}

func NewTabletUpdateRouter() TabletUpdateRouter {
	return &tabletUpdateRouter{}
}

func (tur *tabletUpdateRouter) Register(r *gin.RouterGroup) {
	// 获取数据库连接
	database, _ := db.GetDB()

	// 初始化各层组件
	tabletUpdateRepository := repository.NewTabletUpdateRepository(database)
	tabletUpdateService := service.NewTabletUpdateService(tabletUpdateRepository)
	tabletUpdateController := handler.NewTabletUpdateController(tabletUpdateService)

	// 教材相关路由
	textbookGroup := r.Group("/textbook")
	{
		textbookGroup.GET("/resource-options", tabletUpdateController.GetTextBookOptions) // 获取教材选项
		textbookGroup.GET("/histories", tabletUpdateController.GetTextBookHistories)      // 获取教材历史记录
	}
}
