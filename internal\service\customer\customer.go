package customer

import (
	"encoding/json"
	"errors"
	"fmt"
	api "marketing-app/internal/api/client/dteacher"
	"marketing-app/internal/api/client/dteacher/config"
	"marketing-app/internal/cache"
	"marketing-app/internal/model"
	"marketing-app/internal/repository/customer"
	"marketing-app/internal/service/customer/entity"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type CustomerSvc interface {
	GetDeviceUsedDetail(c *gin.Context, req *entity.DeviceUsedDetailRequest) (*entity.DeviceUsedDetailResponse, error)
	GetDeviceUsageSummary(c *gin.Context, req *entity.DeviceUsageSummaryRequest) (*entity.DeviceUsageSummaryResponse, error)
	GetDeviceUsageSummaryBatch(c *gin.Context, req *entity.DeviceUsageSummaryBatchRequest) (*entity.DeviceUsageSummaryBatchResponse, error)
	GetTutorUsageSummary(c *gin.Context, req *entity.TutorUsageSummaryRequest) (*entity.TutorUsageSummaryResponse, error)
	GetTutorUsageDetail(c *gin.Context, req *entity.TutorUsageDetailRequest) (*entity.TutorUsageDetailResponse, error)
	GetTutorsplanUsageSummary(c *gin.Context, req *entity.TutorsplanUsageSummaryRequest) (*entity.TutorsplanUsageSummaryResponse, error)
	GetTutorsplanUsageDetail(c *gin.Context, req *entity.TutorsplanUsageDetailRequest) (*entity.TutorsplanUsageDetailResponse, error)
	GetDTeacherData(c *gin.Context, req *entity.DTeacherDataRequest) (*entity.DTeacherDataResponse, error)
	GetDTeacherReportWeekly(c *gin.Context, req *entity.DTeacherReportWeeklyRequest) (*entity.DTeacherReportWeeklyResponse, error)
	GetDTeacherReportWeeks(c *gin.Context, req *entity.DTeacherReportWeeksRequest) (*entity.DTeacherReportWeeksResponse, error)
}

type customerSvc struct {
	customerRepo  customer.Customer
	customerCache cache.CustomerCache
}

func NewCustomerService(customerRepo customer.Customer) CustomerSvc {
	return &customerSvc{
		customerRepo:  customerRepo,
		customerCache: cache.NewCustomerCache(),
	}
}

// GetDeviceUsedDetail 获取设备使用详情
func (s *customerSvc) GetDeviceUsedDetail(c *gin.Context, req *entity.DeviceUsedDetailRequest) (*entity.DeviceUsedDetailResponse, error) {
	// 验证weekEnd是否为周一
	if err := s.validateWeekEnd(req.WeekEnd); err != nil {
		return nil, err
	}

	resp := &entity.DeviceUsedDetailResponse{}

	// 1. 获取保修信息
	warranty, err := s.customerRepo.GetWarrantyInfo(c, req.Number)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf("get warranty info failed: %w", err)
	}

	var buyDate *time.Time
	if warranty != nil && warranty.BuyDate != nil {
		buyDate = warranty.BuyDate
	}

	// 2. 获取设备最后使用信息
	tableName, err := s.customerRepo.GetTableName(c, "device_last_use", req.WeekEnd)
	if err != nil {
		return nil, fmt.Errorf("get table name failed: %w", err)
	}

	if tableName != "" {
		deviceLastUse, err := s.customerRepo.GetDeviceLastUse(c, tableName, req.Number, req.WeekEnd, buyDate)
		if err == nil {
			resp.LastUseTime = deviceLastUse.LastUseTime
			if deviceLastUse.LastWeekUseAppNums != nil {
				resp.LastWeekUseAppNums = *deviceLastUse.LastWeekUseAppNums
			}
			if deviceLastUse.LastWeekUseDuration != nil {
				resp.LastWeekUseDuration = *deviceLastUse.LastWeekUseDuration
			}
		} else if !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("get device last use failed: %w", err)
		}
	}

	// 3. 获取本周使用趋势
	trendTableName, err := s.customerRepo.GetTableName(c, "distribution_of_usage", req.WeekEnd)
	if err != nil {
		return nil, fmt.Errorf("get trend table name failed: %w", err)
	}

	if trendTableName != "" {
		distributionOfUsage, err := s.customerRepo.GetDistributionOfUsage(c, trendTableName, req.Number, req.WeekEnd)
		if err == nil && distributionOfUsage.WeekUsage != "" {
			resp.UsageTrend = json.RawMessage(distributionOfUsage.WeekUsage)
		} else if !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("get distribution of usage failed: %w", err)
		}
	}

	// 4. 获取本周使用喜好
	habitTableName, err := s.customerRepo.GetTableName(c, "device_usage_habit", req.WeekEnd)
	if err != nil {
		return nil, fmt.Errorf("get habit table name failed: %w", err)
	}

	if habitTableName != "" {
		deviceUsageHabit, err := s.customerRepo.GetDeviceUsageHabit(c, habitTableName, req.Number, req.WeekEnd)
		if err == nil && deviceUsageHabit.WeekUsage != "" {
			resp.UsagePrefer = json.RawMessage(deviceUsageHabit.WeekUsage)
		} else if !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("get device usage habit failed: %w", err)
		}
	}

	// 5. 获取常用应用top10
	top10TableName, err := s.customerRepo.GetTableName(c, "device_top10_use", req.WeekEnd)
	if err != nil {
		return nil, fmt.Errorf("get top10 table name failed: %w", err)
	}

	if top10TableName != "" {
		deviceTop10Use, err := s.customerRepo.GetDeviceTop10Use(c, top10TableName, req.Number, req.WeekEnd)
		if err == nil {
			if deviceTop10Use.Readboy != "" {
				resp.UsageTop10Readboy = json.RawMessage(deviceTop10Use.Readboy)
			}
			if deviceTop10Use.NonReadboy != "" {
				resp.UsageTop10NonReadboy = json.RawMessage(deviceTop10Use.NonReadboy)
			}
		} else if !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("get device top10 use failed: %w", err)
		}
	}

	return resp, nil
}

// validateWeekEnd 验证weekEnd是否为周一
func (s *customerSvc) validateWeekEnd(weekEnd string) error {
	if len(weekEnd) != 8 {
		return fmt.Errorf("日期格式错误，应为YYYYMMDD格式")
	}

	// 解析日期
	year, err := strconv.Atoi(weekEnd[:4])
	if err != nil {
		return fmt.Errorf("年份格式错误")
	}
	month, err := strconv.Atoi(weekEnd[4:6])
	if err != nil {
		return fmt.Errorf("月份格式错误")
	}
	day, err := strconv.Atoi(weekEnd[6:8])
	if err != nil {
		return fmt.Errorf("日期格式错误")
	}

	weekendTime := time.Date(year, time.Month(month), day, 0, 0, 0, 0, time.UTC)
	if weekendTime.Weekday() != time.Monday {
		return fmt.Errorf("请选择当前周周一时间")
	}

	return nil
}

// GetDeviceUsageSummary 获取设备使用总览
func (s *customerSvc) GetDeviceUsageSummary(c *gin.Context, req *entity.DeviceUsageSummaryRequest) (*entity.DeviceUsageSummaryResponse, error) {
	// 调用Repository层获取用户画像数据
	clientProfile, err := s.customerRepo.GetClientProfile(c, req.Number)
	if err != nil {
		return nil, err
	}

	// 处理apps字段JSON解析
	var apps json.RawMessage
	if clientProfile.Apps != "" {
		// 尝试解析JSON，如果失败则设置为空数组
		if json.Unmarshal([]byte(clientProfile.Apps), &apps) != nil {
			apps = json.RawMessage("[]")
		}
	} else {
		apps = json.RawMessage("[]")
	}

	// 转换为entity响应
	resp := &entity.DeviceUsageSummaryResponse{
		Duration:        clientProfile.Duration,
		LastUseDate:     clientProfile.LastUseDate,
		Apps:            apps,
		UseDays:         clientProfile.UseDays,
		AvgDayDuration:  clientProfile.AvgDayDuration,
		FavoriteSubject: clientProfile.FavoriteSubject,
	}

	return resp, nil
}

// GetDeviceUsageSummaryBatch 批量获取设备使用总览
func (s *customerSvc) GetDeviceUsageSummaryBatch(c *gin.Context, req *entity.DeviceUsageSummaryBatchRequest) (*entity.DeviceUsageSummaryBatchResponse, error) {
	// 调用Repository层批量获取用户画像数据
	clientProfiles, err := s.customerRepo.GetClientProfileBatch(c, req.Numbers)
	if err != nil {
		return nil, err
	}

	// 构建返回数据的map结构
	dataMap := make(map[string]entity.DeviceUsageSummaryItem)

	// 遍历查询结果，构建map
	for _, profile := range clientProfiles {
		// 处理apps字段JSON解析
		var apps json.RawMessage
		if profile.Apps != "" {
			// 尝试解析JSON，如果失败则设置为空数组
			if json.Unmarshal([]byte(profile.Apps), &apps) != nil {
				apps = json.RawMessage("[]")
			}
		} else {
			apps = json.RawMessage("[]")
		}

		// 构建DeviceUsageSummaryItem
		item := entity.DeviceUsageSummaryItem{
			Duration:        profile.Duration,
			LastUseDate:     profile.LastUseDate,
			Apps:            apps,
			UseDays:         profile.UseDays,
			AvgDayDuration:  profile.AvgDayDuration,
			FavoriteSubject: profile.FavoriteSubject,
		}

		dataMap[profile.DeviceID] = item
	}

	// 构建响应
	resp := &entity.DeviceUsageSummaryBatchResponse{
		Data: dataMap,
	}

	return resp, nil
}

// GetTutorUsageSummary 获取教材全解使用总览
func (s *customerSvc) GetTutorUsageSummary(c *gin.Context, req *entity.TutorUsageSummaryRequest) (*entity.TutorUsageSummaryResponse, error) {
	// 验证weekEnd是否为周一
	if err := s.validateWeekEnd(req.WeekEnd); err != nil {
		return nil, err
	}

	// 获取表名
	tableName, err := s.customerRepo.GetTableName(c, "tutor_usage_summary", req.WeekEnd)
	if err != nil {
		return nil, fmt.Errorf("get table name failed: %w", err)
	}

	if tableName == "" {
		return nil, nil
	}

	// 查询教材全解使用数据
	tutorUsage, err := s.customerRepo.GetTutorUsageSummary(c, tableName, req.Number, req.WeekEnd)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("get tutor usage summary failed: %w", err)
	}

	resp := &entity.TutorUsageSummaryResponse{}

	// 处理语文科目数据
	if tutorUsage.Chinese != "" {
		if s.isSubjectDataEmpty(tutorUsage.Chinese, "chinese") {
			resp.Chinese = nil
		} else {
			chinese := json.RawMessage(tutorUsage.Chinese)
			resp.Chinese = &chinese
		}
	}

	// 处理数学科目数据
	if tutorUsage.Math != "" {
		if s.isSubjectDataEmpty(tutorUsage.Math, "math") {
			resp.Math = nil
		} else {
			math := json.RawMessage(tutorUsage.Math)
			resp.Math = &math
		}
	}

	// 处理英语科目数据
	if tutorUsage.English != "" {
		if s.isSubjectDataEmpty(tutorUsage.English, "english") {
			resp.English = nil
		} else {
			english := json.RawMessage(tutorUsage.English)
			resp.English = &english
		}
	}

	// 处理科学科目数据
	if tutorUsage.Science != "" {
		if s.isSubjectDataEmpty(tutorUsage.Science, "science") {
			resp.Science = nil
		} else {
			science := json.RawMessage(tutorUsage.Science)
			resp.Science = &science
		}
	}

	// 如果所有科目都为空，返回nil
	if resp.Chinese == nil && resp.Math == nil && resp.English == nil && resp.Science == nil {
		return nil, nil
	}

	return resp, nil
}

// isSubjectDataEmpty 检查科目数据是否为空（所有字段都为"0"）
func (s *customerSvc) isSubjectDataEmpty(jsonData string, subject string) bool {
	var data map[string]interface{}
	if err := json.Unmarshal([]byte(jsonData), &data); err != nil {
		return true
	}
	switch subject {
	case "chinese":
		return data["video_duration"] == "0" && data["word_duration"] == "0" &&
			data["video_nums"] == "0" && data["recite_duration"] == "0" && data["section_nums"] == "0"
	case "math":
		return data["question_nums"] == "0" && data["video_duration"] == "0" &&
			data["video_nums"] == "0" && data["section_nums"] == "0"
	case "english":
		return data["video_duration"] == "0" && data["zwting_duration"] == "0" &&
			data["video_nums"] == "0" && data["section_nums"] == "0"
	case "science":
		return data["video_duration"] == "0" && data["video_nums"] == "0" && data["section_nums"] == "0"
	default:
		return true
	}
}

// GetTutorUsageDetail 获取教材全解使用详情
func (s *customerSvc) GetTutorUsageDetail(c *gin.Context, req *entity.TutorUsageDetailRequest) (*entity.TutorUsageDetailResponse, error) {
	// 验证weekEnd是否为周一
	if err := s.validateWeekEnd(req.WeekEnd); err != nil {
		return nil, err
	}

	// 验证科目类型
	if req.Subject < 1 || req.Subject > 4 {
		return nil, fmt.Errorf("科目类型错误")
	}

	// 获取表名
	tableName, err := s.customerRepo.GetTableName(c, "tutor_usage_detail", req.WeekEnd)
	if err != nil {
		return nil, fmt.Errorf("get table name failed: %w", err)
	}

	// 如果表不存在，返回空数组
	if tableName == "" {
		return &entity.TutorUsageDetailResponse{
			Data: json.RawMessage("[]"),
		}, nil
	}

	// 查询教材全解使用详情数据
	tutorDetail, err := s.customerRepo.GetTutorUsageDetail(c, tableName, req.Number, req.WeekEnd)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &entity.TutorUsageDetailResponse{
				Data: json.RawMessage("[]"),
			}, nil
		}
		return nil, fmt.Errorf("get tutor usage detail failed: %w", err)
	}

	// 根据科目类型获取对应的数据
	var subjectData string
	switch req.Subject {
	case 1: // 语文
		subjectData = tutorDetail.Chinese
	case 2: // 数学
		subjectData = tutorDetail.Math
	case 3: // 英语
		subjectData = tutorDetail.English
	case 4: // 科学
		subjectData = tutorDetail.Science
	}

	// 如果没有数据，返回空数组
	if subjectData == "" {
		return &entity.TutorUsageDetailResponse{
			Data: json.RawMessage("[]"),
		}, nil
	}

	// 验证JSON格式并返回
	var jsonData interface{}
	if err := json.Unmarshal([]byte(subjectData), &jsonData); err != nil {
		return &entity.TutorUsageDetailResponse{
			Data: json.RawMessage("[]"),
		}, nil
	}

	return &entity.TutorUsageDetailResponse{
		Data: json.RawMessage(subjectData),
	}, nil
}

// GetTutorsplanUsageSummary 获取名师辅导班使用总览
func (s *customerSvc) GetTutorsplanUsageSummary(c *gin.Context, req *entity.TutorsplanUsageSummaryRequest) (*entity.TutorsplanUsageSummaryResponse, error) {
	// 验证weekEnd是否为周一
	if err := s.validateWeekEnd(req.WeekEnd); err != nil {
		return nil, err
	}

	// 获取表名
	tableName, err := s.customerRepo.GetTableName(c, "tutorsplan_usage_summary", req.WeekEnd)
	if err != nil {
		return nil, fmt.Errorf("get table name failed: %w", err)
	}

	// 如果表不存在，返回空数组（与Python保持一致）
	if tableName == "" {
		return nil, nil
	}

	// 查询名师辅导班使用数据
	tutorsplanUsage, err := s.customerRepo.GetTutorsplanUsageSummary(c, tableName, req.Number, req.WeekEnd)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("get tutorsplan usage summary failed: %w", err)
	}

	resp := &entity.TutorsplanUsageSummaryResponse{}

	// 处理语文科目数据
	if tutorsplanUsage.Chinese != "" {
		if s.isTutorsplanSubjectDataEmpty(tutorsplanUsage.Chinese) {
			resp.Chinese = &json.RawMessage{'{', '}'}
		} else {
			chinese := json.RawMessage(tutorsplanUsage.Chinese)
			resp.Chinese = &chinese
		}
	}

	// 处理数学科目数据
	if tutorsplanUsage.Math != "" {
		if s.isTutorsplanSubjectDataEmpty(tutorsplanUsage.Math) {
			resp.Math = &json.RawMessage{'{', '}'}
		} else {
			math := json.RawMessage(tutorsplanUsage.Math)
			resp.Math = &math
		}
	}

	// 处理英语科目数据
	if tutorsplanUsage.English != "" {
		if s.isTutorsplanSubjectDataEmpty(tutorsplanUsage.English) {
			resp.English = &json.RawMessage{'{', '}'}
		} else {
			english := json.RawMessage(tutorsplanUsage.English)
			resp.English = &english
		}
	}

	// 处理其他科目数据
	if tutorsplanUsage.Other != "" {
		if s.isTutorsplanSubjectDataEmpty(tutorsplanUsage.Other) {
			resp.Other = &json.RawMessage{'{', '}'}
		} else {
			other := json.RawMessage(tutorsplanUsage.Other)
			resp.Other = &other
		}
	}

	// 检查是否所有科目都为空（与Python逻辑保持一致）
	emptyJSON := json.RawMessage("{}")
	if (resp.Chinese == nil || string(*resp.Chinese) == string(emptyJSON)) &&
		(resp.Math == nil || string(*resp.Math) == string(emptyJSON)) &&
		(resp.English == nil || string(*resp.English) == string(emptyJSON)) &&
		(resp.Other == nil || string(*resp.Other) == string(emptyJSON)) {
		return nil, nil
	}

	return resp, nil
}

// isTutorsplanSubjectDataEmpty 检查名师辅导班科目数据是否为空（所有字段都为"0"）
func (s *customerSvc) isTutorsplanSubjectDataEmpty(jsonData string) bool {
	var data map[string]interface{}
	if err := json.Unmarshal([]byte(jsonData), &data); err != nil {
		return true
	}

	return data["course_nums"] == "0" && data["duration"] == "0" &&
		data["section_nums"] == "0" && data["video_nums"] == "0"
}

// GetTutorsplanUsageDetail 获取名师辅导班使用详情
func (s *customerSvc) GetTutorsplanUsageDetail(c *gin.Context, req *entity.TutorsplanUsageDetailRequest) (*entity.TutorsplanUsageDetailResponse, error) {
	// 验证weekEnd是否为周一
	if err := s.validateWeekEnd(req.WeekEnd); err != nil {
		return nil, err
	}

	// 验证科目类型（注意这里1-4对应chinese/math/english/other）
	if req.Subject < 1 || req.Subject > 4 {
		return nil, fmt.Errorf("科目类型错误")
	}

	// 获取表名
	tableName, err := s.customerRepo.GetTableName(c, "tutorsplan_usage_detail", req.WeekEnd)
	if err != nil {
		return nil, fmt.Errorf("get table name failed: %w", err)
	}

	// 如果表不存在，返回空数组
	if tableName == "" {
		return &entity.TutorsplanUsageDetailResponse{
			Data: json.RawMessage("[]"),
		}, nil
	}

	// 查询名师辅导班使用详情数据
	tutorsplanDetail, err := s.customerRepo.GetTutorsplanUsageDetail(c, tableName, req.Number, req.WeekEnd)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &entity.TutorsplanUsageDetailResponse{
				Data: json.RawMessage("[]"),
			}, nil
		}
		return nil, fmt.Errorf("get tutorsplan usage detail failed: %w", err)
	}

	// 根据科目类型获取对应的数据
	var subjectData string
	switch req.Subject {
	case 1: // 语文
		subjectData = tutorsplanDetail.Chinese
	case 2: // 数学
		subjectData = tutorsplanDetail.Math
	case 3: // 英语
		subjectData = tutorsplanDetail.English
	case 4: // 其他
		subjectData = tutorsplanDetail.Other
	}

	// 如果没有数据，返回空数组
	if subjectData == "" {
		return &entity.TutorsplanUsageDetailResponse{
			Data: json.RawMessage("[]"),
		}, nil
	}

	// 验证JSON格式并返回
	var jsonData interface{}
	if err := json.Unmarshal([]byte(subjectData), &jsonData); err != nil {
		return &entity.TutorsplanUsageDetailResponse{
			Data: json.RawMessage("[]"),
		}, nil
	}

	return &entity.TutorsplanUsageDetailResponse{
		Data: json.RawMessage(subjectData),
	}, nil
}

// GetDTeacherData 获取双师数据
func (s *customerSvc) GetDTeacherData(c *gin.Context, req *entity.DTeacherDataRequest) (*entity.DTeacherDataResponse, error) {
	// 1. 首先尝试从缓存获取数据
	cachedData, err := s.customerCache.GetDTeacherData(c, req.UserID)
	if err == nil && cachedData != nil {
		// 缓存命中，直接返回数据
		if data, ok := cachedData.(map[string]interface{}); ok {
			return &entity.DTeacherDataResponse{
				Data: data,
			}, nil
		}
	}

	// 2. 缓存未命中，调用API获取数据
	cfg := config.LoadDTeacherConfig()
	client := api.NewDTeacherClient(cfg)

	apiData, err := client.GetDTeacherData(c, req.UserID)
	if err != nil {
		return nil, fmt.Errorf("get dteacher data failed: %w", err)
	}

	// 3. API调用成功，将数据存入缓存
	_ = s.customerCache.SetDTeacherData(c, apiData, req.UserID)

	return &entity.DTeacherDataResponse{
		Data: apiData,
	}, nil
}

// GetDTeacherReportWeekly 获取双师周报数据
func (s *customerSvc) GetDTeacherReportWeekly(c *gin.Context, req *entity.DTeacherReportWeeklyRequest) (*entity.DTeacherReportWeeklyResponse, error) {
	// 1. 时间转换和处理
	startDay := time.Unix(req.StartDay, 0)
	dateStr := startDay.Format("2006-01-02")

	// 2. 首先尝试从缓存获取数据
	cachedData, err := s.customerCache.GetDTeacherReportWeekly(c, strconv.Itoa(req.UserID), dateStr)
	if err == nil && cachedData != nil {
		// 缓存命中，直接返回数据
		if data, ok := cachedData.(map[string]interface{}); ok {
			return &entity.DTeacherReportWeeklyResponse{
				Data: data,
			}, nil
		}
	}

	// 3. 判断请求的日期是否属于本周
	now := time.Now()
	weekday := int(now.Weekday())
	if weekday == 0 { // 周日调整为7
		weekday = 7
	}
	monday := now.AddDate(0, 0, -weekday+1) // 本周周一

	if startDay.After(monday) || startDay.Equal(monday.Truncate(24*time.Hour)) {
		// 4. 本周数据：直接调用API
		cfg := config.LoadDTeacherConfig()
		client := api.NewDTeacherClient(cfg)

		apiData, err := client.GetDTeacherReportWeekly(c, req.UserID, req.StartDay)
		if err != nil {
			return nil, fmt.Errorf("get dteacher report weekly failed: %w", err)
		}

		// 5. API调用成功，将数据存入缓存
		_ = s.customerCache.SetDTeacherReportWeekly(c, apiData, strconv.Itoa(req.UserID), dateStr)

		return &entity.DTeacherReportWeeklyResponse{
			Data: apiData,
		}, nil
	} else {
		// 6. 历史数据：先查数据库
		dbReport, err := s.customerRepo.GetDTeacherReportWeekly(c, req.UserID, startDay)
		if err == nil && dbReport != nil {
			// 数据库中有数据，构造响应并存入缓存
			responseData := s.buildDTeacherReportResponse(dbReport)
			_ = s.customerCache.SetDTeacherReportWeekly(c, responseData, strconv.Itoa(req.UserID), dateStr)

			return &entity.DTeacherReportWeeklyResponse{
				Data: responseData,
			}, nil
		}

		// 7. 数据库没有数据，调用API获取
		cfg := config.LoadDTeacherConfig()
		client := api.NewDTeacherClient(cfg)

		apiData, err := client.GetDTeacherReportWeekly(c, req.UserID, req.StartDay)
		if err != nil {
			return nil, fmt.Errorf("get dteacher report weekly failed: %w", err)
		}

		// 8. 存入缓存
		_ = s.customerCache.SetDTeacherReportWeekly(c, apiData, strconv.Itoa(req.UserID), dateStr)

		// 9. 存入数据库
		dbRecord := s.buildDTeacherReportRecord(apiData, req.UserID, startDay)
		_ = s.customerRepo.AddDTeacherReportWeekly(c, dbRecord)

		return &entity.DTeacherReportWeeklyResponse{
			Data: apiData,
		}, nil
	}
}

// buildDTeacherReportResponse 从数据库记录构建响应数据
func (s *customerSvc) buildDTeacherReportResponse(dbReport *model.DTeacherReport) map[string]interface{} {
	// 构建周期字符串
	weekendEnd := dbReport.Weekend.AddDate(0, 0, 6)
	weekendStart := dbReport.Weekend
	weekRange := fmt.Sprintf("%s-%s", weekendStart.Format("2006.01.02"), weekendEnd.Format("2006.01.02"))

	result := map[string]interface{}{
		"weekend": weekRange,
	}

	if dbReport.Comment != nil {
		result["comment"] = *dbReport.Comment
	}

	if dbReport.LessonList != nil {
		var lessonList interface{}
		json.Unmarshal([]byte(*dbReport.LessonList), &lessonList)
		result["lesson_list"] = lessonList
	}

	if dbReport.ParticipatedNum != nil {
		result["participated_num"] = *dbReport.ParticipatedNum
	}

	if dbReport.ParticipatedTime != nil {
		result["participated_time"] = *dbReport.ParticipatedTime
	}

	if dbReport.SubjectsLessonsNumInfo != nil {
		var subjectsInfo interface{}
		json.Unmarshal([]byte(*dbReport.SubjectsLessonsNumInfo), &subjectsInfo)
		result["subjects_lessons_num_info"] = subjectsInfo
	}

	return result
}

// buildDTeacherReportRecord 从API数据构建数据库记录
func (s *customerSvc) buildDTeacherReportRecord(apiData map[string]interface{}, userID int, weekend time.Time) *model.DTeacherReport {
	record := &model.DTeacherReport{
		UID:     userID,
		Weekend: weekend,
	}

	if comment, ok := apiData["comment"].(string); ok {
		record.Comment = &comment
	}

	if lessonList, ok := apiData["lesson_list"]; ok {
		if lessonListBytes, err := json.Marshal(lessonList); err == nil {
			lessonListStr := string(lessonListBytes)
			record.LessonList = &lessonListStr
		}
	}

	if participatedNum, ok := apiData["participated_num"].(float64); ok {
		num := int(participatedNum)
		record.ParticipatedNum = &num
	}

	if participatedTime, ok := apiData["participated_time"].(float64); ok {
		time := int(participatedTime)
		record.ParticipatedTime = &time
	}

	if subjectsInfo, ok := apiData["subjects_lessons_num_info"]; ok {
		if subjectsInfoBytes, err := json.Marshal(subjectsInfo); err == nil {
			subjectsInfoStr := string(subjectsInfoBytes)
			record.SubjectsLessonsNumInfo = &subjectsInfoStr
		}
	}

	return record
}

// GetDTeacherReportWeeks 获取双师周数据列表
func (s *customerSvc) GetDTeacherReportWeeks(c *gin.Context, req *entity.DTeacherReportWeeksRequest) (*entity.DTeacherReportWeeksResponse, error) {
	// 1. 首先尝试从缓存获取数据
	cachedData, err := s.customerCache.GetDTeacherReportWeeks(c, strconv.Itoa(req.UserID))
	if err == nil && cachedData != nil {
		// 缓存命中，直接返回数据
		if data, ok := cachedData.([]interface{}); ok {
			return &entity.DTeacherReportWeeksResponse{
				Data: data,
			}, nil
		}
	}

	// 2. 缓存未命中，调用API获取数据
	cfg := config.LoadDTeacherConfig()
	client := api.NewDTeacherClient(cfg)

	apiData, err := client.GetDTeacherReportWeeks(c, req.UserID)
	if err != nil {
		return nil, fmt.Errorf("get dteacher report weeks failed: %w", err)
	}

	// 3. API调用成功，将数据存入缓存
	_ = s.customerCache.SetDTeacherReportWeeks(c, apiData, strconv.Itoa(req.UserID))

	return &entity.DTeacherReportWeeksResponse{
		Data: apiData,
	}, nil
}
