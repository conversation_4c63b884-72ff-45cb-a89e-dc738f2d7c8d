package operation

import (
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"marketing-app/internal/repository"
	"marketing-app/internal/repository/operation"
	repoDto "marketing-app/internal/repository/operation/dto"
)

type OperationSvc interface {
	GetActivities(c *gin.Context, uid uint, date string) ([]repoDto.ActivityListItem, error)
	GetSalesStatistics(c *gin.Context, uid uint, date string) (*repoDto.SalesStatistics, error)
	GetUserProfile(c *gin.Context, userID uint) (*repoDto.UserProfile, error)
	GetArticles(c *gin.Context, uid uint, categoryID uint, tagID uint, customerVisible *uint8, keyword string, page int, pageSize int) ([]*repoDto.ArticleListItem, error)
	GetArticleDetail(c *gin.Context, uid uint, articleID uint) (*repoDto.ArticleDetailItem, error)
	GetCategories(c *gin.Context, uid uint, parentID uint, customerVisible *uint8) ([]*repoDto.CategoryListItem, error)
	SearchArticles(c *gin.Context, keyword string) ([]*repoDto.ArticleSearchItem, error)
	LikeArticle(c *gin.Context, uid uint, articleID uint, like uint8) error
	ViewArticles(c *gin.Context, uid uint, articleIDs []uint) error
	DownloadArticle(c *gin.Context, uid uint, articleID uint) error
	GetArticleComments(c *gin.Context, uid uint, articleID uint, page int, pageSize int, replySize uint, orderBy string, order string) ([]*repoDto.ArticleCommentItem, error)
}

type operationSvc struct {
	operationRepo operation.Operation
	adminUserRepo repository.AdminUserRepository
}

func NewOperationService(operationRepo operation.Operation, adminUserRepo repository.AdminUserRepository) OperationSvc {
	return &operationSvc{
		operationRepo: operationRepo,
		adminUserRepo: adminUserRepo,
	}
}

// GetActivities 获取活动列表
func (o *operationSvc) GetActivities(c *gin.Context, uid uint, date string) ([]repoDto.ActivityListItem, error) {
	// 验证日期格式
	if len(date) != 10 {
		return nil, errors.New("日期格式应为YYYY-MM-DD")
	}

	// 获取用户终端信息
	endpoint, err := o.adminUserRepo.GetUserEndpoint(c, uid)
	if err != nil {
		return nil, errors.Wrap(err, "获取用户终端信息失败")
	}
	if endpoint == nil {
		return nil, errors.New("用户未绑定终端")
	}

	endpointID := uint(endpoint.ID)
	return o.operationRepo.GetActivities(c, endpointID, date)
}

// GetSalesStatistics 获取销售统计
func (o *operationSvc) GetSalesStatistics(c *gin.Context, uid uint, date string) (*repoDto.SalesStatistics, error) {
	// 验证日期格式
	if len(date) != 10 {
		return nil, errors.New("日期格式应为YYYY-MM-DD")
	}

	// 获取用户终端信息
	endpoint, err := o.adminUserRepo.GetUserEndpoint(c, uid)
	if err != nil {
		return nil, errors.Wrap(err, "获取用户终端信息失败")
	}
	if endpoint == nil {
		return nil, errors.New("用户未绑定终端")
	}

	endpointID := uint(endpoint.ID)
	return o.operationRepo.GetSalesStatistics(c, endpointID, date)
}

// GetUserProfile 获取用户资料
func (o *operationSvc) GetUserProfile(c *gin.Context, userID uint) (*repoDto.UserProfile, error) {
	// 参数验证
	if userID == 0 {
		return nil, errors.New("用户ID不能为空")
	}

	return o.operationRepo.GetUserProfile(c, userID)
}

// GetArticles 获取文章列表
func (o *operationSvc) GetArticles(c *gin.Context, uid uint, categoryID uint, tagID uint, customerVisible *uint8, keyword string, page int, pageSize int) ([]*repoDto.ArticleListItem, error) {
	// 获取用户的ResourceID
	resourceID, err := o.getUserResourceID(c, uid)
	if err != nil {
		return nil, errors.Wrap(err, "获取用户资源ID失败")
	}

	// 构建查询参数
	query := &repoDto.ArticleListQuery{
		UserID:          uid,
		ResourceID:      resourceID,
		CategoryID:      categoryID,
		TagID:           tagID,
		CustomerVisible: customerVisible,
		Keyword:         keyword,
		Page:            page,
		PageSize:        pageSize,
	}

	// 设置默认分页参数
	if query.Page <= 0 {
		query.Page = 1
	}
	if query.PageSize <= 0 {
		query.PageSize = 20
	}
	if query.PageSize > 100 {
		query.PageSize = 100
	}

	return o.operationRepo.GetArticles(c, query)
}

// GetArticleDetail 获取文章详情
func (o *operationSvc) GetArticleDetail(c *gin.Context, uid uint, articleID uint) (*repoDto.ArticleDetailItem, error) {
	// 参数验证
	if articleID == 0 {
		return nil, errors.New("文章ID不能为空")
	}

	// 获取用户的ResourceID
	resourceID, err := o.getUserResourceID(c, uid)
	if err != nil {
		return nil, errors.Wrap(err, "获取用户资源ID失败")
	}

	// 调用Repository层获取文章详情
	article, err := o.operationRepo.GetArticleDetail(c, articleID, uid, resourceID)
	if err != nil {
		return nil, errors.Wrap(err, "获取文章详情失败")
	}

	return article, nil
}

// getUserResourceID 获取用户的ResourceID
func (o *operationSvc) getUserResourceID(c *gin.Context, uid uint) (uint, error) {
	// 调用repository层的方法获取用户ResourceID
	resourceID, err := o.adminUserRepo.GetUserResourceID(c, uid)
	if err != nil {
		return 0, errors.Wrap(err, "获取用户资源ID失败")
	}

	return resourceID, nil
}

// GetCategories 获取分类列表
func (o *operationSvc) GetCategories(c *gin.Context, uid uint, parentID uint, customerVisible *uint8) ([]*repoDto.CategoryListItem, error) {
	// 获取用户的ResourceID
	resourceID, err := o.getUserResourceID(c, uid)
	if err != nil {
		return nil, errors.Wrap(err, "获取用户资源ID失败")
	}

	// 构建查询参数
	query := &repoDto.CategoryListQuery{
		ParentID:        parentID,
		CustomerVisible: customerVisible,
		ResourceID:      resourceID,
	}

	return o.operationRepo.GetCategories(c, query)
}

// SearchArticles 搜索文章
func (o *operationSvc) SearchArticles(c *gin.Context, keyword string) ([]*repoDto.ArticleSearchItem, error) {
	// 参数验证
	if strings.TrimSpace(keyword) == "" {
		return nil, errors.New("keyword不能为空")
	}

	// 调用Repository层进行搜索
	articles, err := o.operationRepo.SearchArticles(c, keyword)
	if err != nil {
		return nil, errors.Wrap(err, "搜索文章失败")
	}

	return articles, nil
}

// LikeArticle 点赞或取消点赞文章
func (o *operationSvc) LikeArticle(c *gin.Context, uid uint, articleID uint, like uint8) error {
	// 参数验证
	if uid == 0 {
		return errors.New("用户ID不能为空")
	}
	if articleID == 0 {
		return errors.New("文章ID不能为空")
	}
	if like != 0 && like != 1 {
		return errors.New("like参数只能为0或1")
	}

	// 调用Repository层进行点赞操作
	err := o.operationRepo.LikeArticle(c, uid, articleID, like)
	if err != nil {
		return errors.Wrap(err, "点赞操作失败")
	}

	return nil
}

// ViewArticles 批量浏览文章
func (o *operationSvc) ViewArticles(c *gin.Context, uid uint, articleIDs []uint) error {
	// 参数验证
	if uid == 0 {
		return errors.New("用户ID不能为空")
	}
	if len(articleIDs) == 0 {
		return errors.New("文章ID列表不能为空")
	}
	
	// 验证文章ID有效性
	for _, articleID := range articleIDs {
		if articleID == 0 {
			return errors.New("文章ID必须大于0")
		}
	}

	// 调用Repository层记录浏览
	err := o.operationRepo.ViewArticles(c, uid, articleIDs)
	if err != nil {
		return errors.Wrap(err, "记录文章浏览失败")
	}

	return nil
}

// DownloadArticle 下载文章
func (o *operationSvc) DownloadArticle(c *gin.Context, uid uint, articleID uint) error {
	// 参数验证
	if uid == 0 {
		return errors.New("用户ID不能为空")
	}
	if articleID == 0 {
		return errors.New("文章ID不能为空")
	}

	// 调用Repository层记录下载
	err := o.operationRepo.DownloadArticle(c, uid, articleID)
	if err != nil {
		return errors.Wrap(err, "下载文章失败")
	}

	return nil
}

// GetArticleComments 获取文章评论列表
func (o *operationSvc) GetArticleComments(c *gin.Context, uid uint, articleID uint, page int, pageSize int, replySize uint, orderBy string, order string) ([]*repoDto.ArticleCommentItem, error) {
	// 参数验证
	if uid == 0 {
		return nil, errors.New("用户ID不能为空")
	}
	if articleID == 0 {
		return nil, errors.New("文章ID不能为空")
	}
	
	// 设置默认分页参数
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}
	if pageSize > 100 {
		pageSize = 100
	}
	if replySize == 0 {
		replySize = 3
	}
	if replySize > 20 {
		replySize = 20
	}
	
	// 验证排序参数
	if orderBy != "" && orderBy != "created_at" && orderBy != "num_likes" {
		return nil, errors.New("排序字段只能为created_at或num_likes")
	}
	if orderBy != "" && order != "asc" && order != "desc" {
		return nil, errors.New("排序方向只能为asc或desc")
	}
	
	// 构建查询参数
	query := &repoDto.ArticleCommentListQuery{
		ArticleID: articleID,
		UserID:    uid,
		Page:      page,
		PageSize:  pageSize,
		ReplySize: replySize,
		OrderBy:   orderBy,
		Order:     order,
	}
	
	// 调用Repository层获取评论列表
	comments, err := o.operationRepo.GetArticleComments(c, query)
	if err != nil {
		return nil, errors.Wrap(err, "获取文章评论列表失败")
	}
	
	return comments, nil
}
