package service

import (
	"encoding/json"
	"fmt"
	"marketing-app/internal/handler/dto"
	"marketing-app/internal/pkg/config"
	"marketing-app/internal/pkg/log"
	"marketing-app/internal/pkg/utils"
	"marketing-app/internal/repository"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"go.uber.org/zap"
)

// TabletUpdateFormattedData 格式化后的平板更新数据
type TabletUpdateFormattedData struct {
	Title string                              `json:"title"`
	Type  string                              `json:"type"`
	Time  string                              `json:"time"`
	Rows  map[string][]map[string]interface{} `json:"rows,omitempty"` // 课程类型分组
	Data  []map[string]interface{}            `json:"data,omitempty"` // 应用更新数据
}

type TabletUpdateService interface {
	// GetTabletUpdateDetail 获取平板更新详情
	GetTabletUpdateDetail(c *gin.Context, req *dto.TabletUpdateDetailRequest) (*dto.TabletUpdateDetailResponse, error)
	// GetTextBookOptions 获取教材选项
	GetTextBookOptions(c *gin.Context) ([]dto.TextBookOptionsResponse, error)
	// GetTextBookHistories 获取教材历史记录
	GetTextBookHistories(c *gin.Context, req *dto.TextBookHistoriesRequest) ([]dto.TextBookHistoriesGroupItem, error)
}

type tabletUpdateService struct {
	tabletUpdateRepo repository.TabletUpdateRepository
}

func NewTabletUpdateService(tabletUpdateRepo repository.TabletUpdateRepository) TabletUpdateService {
	return &tabletUpdateService{
		tabletUpdateRepo: tabletUpdateRepo,
	}
}

// FormatTabletUpdateData 格式化平板更新数据，可在整个service包中使用
// 该方法解密参数、获取数据并按照HTML模板格式整理title和content
func FormatTabletUpdateData(c *gin.Context, encryptedParams, timeStr string, repo repository.TabletUpdateRepository) (*TabletUpdateFormattedData, error) {
	// 验证参数是否为十六进制
	if !utils.IsHexString(encryptedParams) {
		return nil, errors.New("参数错误")
	}

	// 验证时间格式
	if _, err := time.Parse("2006-01-02 15:04:05", timeStr); err != nil {
		return nil, errors.New("时间格式错误")
	}

	// 解密参数
	des := utils.NewDESCrypto(config.GetString("TABLET_UPDATE_DES_KEY"), config.GetString("TABLET_UPDATE_DES_IV"))
	if des == nil {
		return nil, errors.New("参数解密失败")
	}
	decryptedParams, err := des.Decrypt(encryptedParams)
	if err != nil {
		return nil, errors.New("参数解密失败")
	}

	// 验证解密后的参数格式（应该是逗号分隔的数字）
	matched, err := regexp.MatchString(`^[1-9]\d*(,[1-9]\d*)*$`, decryptedParams)
	if err != nil || !matched {
		return nil, errors.New("参数格式错误")
	}

	// 解析ID列表
	idStrings := strings.Split(decryptedParams, ",")
	ids := make([]uint, 0, len(idStrings))
	for _, idStr := range idStrings {
		id, err := strconv.ParseUint(strings.TrimSpace(idStr), 10, 32)
		if err != nil {
			return nil, errors.New("参数格式错误")
		}
		ids = append(ids, uint(id))
	}

	// 从数据库获取数据
	data, err := repo.GetTabletUpdatesByIDs(c, ids)
	if err != nil {
		return nil, err
	}

	if len(data) == 0 {
		return nil, errors.New("数据不存在")
	}

	// 处理数据
	var items []dto.TabletUpdateItem
	for _, item := range data {
		// 处理JSON数据：去除多余空格和换行符
		cleanData := regexp.MustCompile(`[ \t]+`).ReplaceAllString(item.Data, "")
		cleanData = regexp.MustCompile(`(\r\n)|\r|\n`).ReplaceAllString(cleanData, "<br/>")

		// 解析JSON
		var jsonData map[string]interface{}
		if err := json.Unmarshal([]byte(cleanData), &jsonData); err != nil {
			log.Error("json解析有误：",
				zap.Uint("id", item.ID),
				zap.String("type", item.Type),
				zap.String("data", item.Data),
			)
			continue
		}

		items = append(items, dto.TabletUpdateItem{
			ID:   item.ID,
			Type: item.Type,
			Data: jsonData,
		})
	}

	if len(items) == 0 {
		return nil, errors.New("数据不存在")
	}

	// 按照HTML模板格式确定标题和类型
	updateType := items[0].Type
	title := formatUpdateTitle(updateType, len(items))

	// 构建格式化数据
	result := &TabletUpdateFormattedData{
		Title: title,
		Type:  updateType,
		Time:  timeStr,
	}

	if updateType != "app" {
		// 课程类型：按type分组，按照HTML模板格式
		rows := make(map[string][]map[string]interface{})
		for _, item := range items {
			if rows[item.Type] == nil {
				rows[item.Type] = make([]map[string]interface{}, 0)
			}
			rows[item.Type] = append(rows[item.Type], item.Data)
		}
		result.Rows = rows
	} else {
		// 应用类型：直接返回数据数组，按照HTML模板格式
		appData := make([]map[string]interface{}, 0, len(items))
		for _, item := range items {
			appData = append(appData, item.Data)
		}
		result.Data = appData
	}

	return result, nil
}

// formatUpdateTitle 根据更新类型和数量格式化标题，按照HTML模板格式
func formatUpdateTitle(updateType string, count int) string {
	var title string
	switch updateType {
	case "app":
		title = "读书郎软件更新"
	default:
		title = "课程上新"
	}
	return fmt.Sprintf("%s(%d)", title, count)
}

// GenerateTabletUpdateContent 返回格式化的内容字符串
func GenerateTabletUpdateContent(c *gin.Context, encryptedParams, timeStr string, repo repository.TabletUpdateRepository) (string, string, error) {
	// 获取格式化数据
	data, err := FormatTabletUpdateData(c, encryptedParams, timeStr, repo)
	if err != nil {
		return "", "", err
	}

	var content strings.Builder

	if data.Type == "app" {
		// 应用更新类型
		if data.Data != nil {
			for i, row := range data.Data {
				appName := getStringValue(row, "appname")
				versionName := getStringValue(row, "versionname")
				devices := getStringValue(row, "devices")
				updateText := getStringValue(row, "updatetext")

				content.WriteString(fmt.Sprintf("%d、%s\n", i+1, appName))
				content.WriteString(fmt.Sprintf("版本：%s\n", versionName))
				content.WriteString(fmt.Sprintf("机型：%s\n", devices))
				content.WriteString("更新说明：\n")
				content.WriteString(fmt.Sprintf("%s\n", updateText))
				if i < len(data.Data)-1 {
					content.WriteString("\n")
				}
			}
		}
	} else {
		// 课程更新类型
		if data.Rows != nil {
			courseTypeIndex := 0
			for courseType, courses := range data.Rows {
				courseTypeText := getCourseTypeText(courseType)

				content.WriteString(fmt.Sprintf("%s\n", courseTypeText))

				for i, course := range courses {
					var courseText string
					switch courseType {
					case "msfd_course":
						gradeName := getStringValue(course, "grade_name")
						subjectName := getStringValue(course, "subject_name")
						bookName := getStringValue(course, "book_name")
						courseText = fmt.Sprintf("[%s][%s]%s", gradeName, subjectName, bookName)
					case "sszb_course":
						gradeNames := getStringArrayValue(course, "grade_names")
						subjectName := getStringValue(course, "subject_name")
						courseName := getStringValue(course, "course_name")
						courseText = fmt.Sprintf("[%s][%s]%s", strings.Join(gradeNames, "|"), subjectName, courseName)
					case "paper":
						name := getStringValue(course, "name")
						courseText = name
					case "homework":
						name := getStringValue(course, "name")
						pressDate := getStringValue(course, "press_date")
						printDate := getStringValue(course, "print_date")
						isbn := getStringValue(course, "isbn")
						courseText = fmt.Sprintf("%s[%s_%s] ISBN:%s", name, pressDate, printDate, isbn)
					}

					content.WriteString(fmt.Sprintf("%d、%s\n", i+1, courseText))
				}

				courseTypeIndex++
				if courseTypeIndex < len(data.Rows) {
					content.WriteString("\n")
				}
			}
		}
	}

	return data.Title, content.String(), nil
}

// GetTabletUpdateDetail 获取平板更新详情
func (s *tabletUpdateService) GetTabletUpdateDetail(c *gin.Context, req *dto.TabletUpdateDetailRequest) (*dto.TabletUpdateDetailResponse, error) {
	// 使用通用方法格式化数据
	formattedData, err := FormatTabletUpdateData(c, req.Params, req.Time, s.tabletUpdateRepo)
	if err != nil {
		return nil, err
	}

	// 转换为响应格式
	response := &dto.TabletUpdateDetailResponse{
		Title: formattedData.Title,
		Type:  formattedData.Type,
		Time:  formattedData.Time,
		Rows:  formattedData.Rows,
		Data:  formattedData.Data,
	}

	return response, nil
}

// getStringValue 从map中安全获取字符串值
func getStringValue(data map[string]interface{}, key string) string {
	if value, exists := data[key]; exists {
		if str, ok := value.(string); ok {
			return str
		}
	}
	return ""
}

// GetTextBookOptions 获取教材选项
func (s *tabletUpdateService) GetTextBookOptions(c *gin.Context) ([]dto.TextBookOptionsResponse, error) {
	options := []dto.TextBookOptionsResponse{
		{
			Name:  "双师直播课",
			Value: "sszb_course",
		},
		{
			Name:  "名师辅导班",
			Value: "msfd_course",
		},
		{
			Name:  "真题试卷",
			Value: "paper",
		},
		{
			Name:  "作业本教辅",
			Value: "homework",
		},
	}
	return options, nil
}

// GetTextBookHistories 获取教材历史记录
func (s *tabletUpdateService) GetTextBookHistories(c *gin.Context, req *dto.TextBookHistoriesRequest) ([]dto.TextBookHistoriesGroupItem, error) {
	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	// 获取不同的更新日期
	dates, err := s.tabletUpdateRepo.GetDistinctUpdateDates(c, req.Type, req.Page, req.PageSize)
	if err != nil {
		return nil, errors.Wrap(err, "获取更新日期失败")
	}

	if len(dates) == 0 {
		return []dto.TextBookHistoriesGroupItem{}, nil
	}

	// 获取日期范围内的所有记录
	startDate := dates[len(dates)-1]
	endDate := dates[0] + " 23:59:59"

	rows, err := s.tabletUpdateRepo.GetUpdatesByTypeAndDateRange(c, req.Type, startDate, endDate)
	if err != nil {
		return nil, errors.Wrap(err, "获取更新记录失败")
	}

	// 按日期分组
	groups := make(map[string][]string)
	for _, row := range rows {
		dateStr := row.UpdatedAt.Format("2006-01-02")
		groups[dateStr] = append(groups[dateStr], row.Data)
	}

	// 构建响应数据
	histories := make([]dto.TextBookHistoriesGroupItem, len(dates))
	for i, date := range dates {
		histories[i].UpdatedAt = date
		dataList := groups[date]
		histories[i].Items = make([]*dto.TextBookHistoryItem, len(dataList))

		for j, strData := range dataList {
			var data map[string]interface{}
			if err := json.Unmarshal([]byte(strData), &data); err != nil {
				log.Error("decode json error", zap.String("data", strData), zap.Error(err))
				continue
			}

			item := &dto.TextBookHistoryItem{}
			switch req.Type {
			case "sszb_course":
				item.Subject = getStringValue(data, "subject_name")
				if gradeNames, exists := data["grade_names"]; exists {
					item.Grades = gradeNames
				} else {
					item.Grades = ""
				}
				item.Name = getStringValue(data, "course_name")
			case "msfd_course":
				item.Subject = getStringValue(data, "subject_name")
				if gradeName, exists := data["grade_name"]; exists {
					item.Grades = gradeName
				} else {
					item.Grades = ""
				}
				item.Name = getStringValue(data, "book_name")
			case "paper":
				item.Subject = ""
				item.Grades = ""
				item.Name = getStringValue(data, "name")
			case "homework":
				item.Subject = ""
				item.Grades = ""
				item.Name = fmt.Sprintf("%s[%s_%s]ISBN:%s",
					getStringValue(data, "name"),
					getStringValue(data, "press_date"),
					getStringValue(data, "print_date"),
					getStringValue(data, "isbn"))
			}
			histories[i].Items[j] = item
		}
	}

	return histories, nil
}

// getStringArrayValue 从map中安全获取字符串数组值
func getStringArrayValue(data map[string]interface{}, key string) []string {
	if value, exists := data[key]; exists {
		if arr, ok := value.([]interface{}); ok {
			result := make([]string, 0, len(arr))
			for _, item := range arr {
				if str, ok := item.(string); ok {
					result = append(result, str)
				}
			}
			return result
		}
	}
	return []string{}
}

// getCourseTypeText 根据课程类型获取显示文本
func getCourseTypeText(courseType string) string {
	switch courseType {
	case "sszb_course":
		return "双师直播课"
	case "msfd_course":
		return "名师辅导班"
	case "paper":
		return "真题试卷"
	case "homework":
		return "作业本教辅"
	default:
		return courseType
	}
}
